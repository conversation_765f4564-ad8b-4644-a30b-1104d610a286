export function Settings() {
  const deepseekIcon = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      style={{ flex: "none", lineHeight: "1" }} // Corrected style prop format
      viewBox="0 0 24 24"
      width="18" // Add width/height for better rendering control
      height="18"
    >
      <path
        fill="#4D6BFE"
        d="M23.748 4.482c-.254-.124-.364.113-.512.234-.051.039-.094.09-.137.136-.372.397-.806.657-1.373.626-.829-.046-1.537.214-2.163.848-.133-.782-.575-1.248-1.247-1.548-.352-.156-.708-.311-.955-.65-.172-.241-.219-.51-.305-.774-.055-.16-.11-.323-.293-.35-.2-.031-.278.136-.356.276-.313.572-.434 1.202-.422 1.84.027 1.436.633 2.58 1.838 3.393.137.093.172.187.129.323-.082.28-.18.552-.266.833-.055.179-.137.217-.329.14a5.526 5.526 0 0 1-1.736-1.18c-.857-.828-1.631-1.742-2.597-2.458a11.365 11.365 0 0 0-.689-.471c-.985-.957.13-1.743.388-1.836.27-.098.093-.432-.779-.428-.872.004-1.67.295-2.687.684a3.055 3.055 0 0 1-.465.137 9.597 9.597 0 0 0-2.883-.102c-1.885.21-3.39 1.102-4.497 2.623C.082 8.606-.231 10.684.152 12.85c.403 2.284 1.569 4.175 3.36 5.653 1.858 1.533 3.997 2.284 6.438 2.14 1.482-.085 3.133-.284 4.994-1.86.47.234.962.327 1.78.397.63.059 1.236-.03 1.705-.128.735-.156.684-.837.419-.961-2.155-1.004-1.682-.595-2.113-.926 1.096-1.296 2.746-2.642 3.392-7.003.05-.347.007-.565 0-.845-.004-.17.035-.237.23-.256a4.173 4.173 0 0 0 1.545-.475c1.396-.763 1.96-2.015 2.093-3.517.02-.23-.004-.467-.247-.588zM11.581 18c-2.089-1.642-3.102-2.183-3.52-2.16-.392.024-.321.471-.235.763.09.288.207.486.371.739.114.167.192.416-.113.603-.673.416-1.842-.14-1.897-.167-1.361-.802-2.5-1.86-3.301-3.307-.774-1.393-1.224-2.887-1.298-4.482-.02-.386.093-.522.477-.592a4.696 4.696 0 0 1 1.529-.039c2.132.312 3.946 1.265 5.468 2.774.868.86 1.525 1.887 2.202 2.891.72 1.066 1.494 2.082 2.48 2.914.348.292.625.514.891.677-.802.09-2.14.11-3.054-.614zm1-6.44a.306.306 0 0 1 .415-.287.302.302 0 0 1 .2.288.306.306 0 0 1-.31.307.303.303 0 0 1-.304-.308zm3.11 1.596c-.2.081-.399.151-.59.16a1.245 1.245 0 0 1-.798-.254c-.274-.23-.47-.358-.552-.758a1.73 1.73 0 0 1 .016-.588c.07-.327-.008-.537-.239-.727-.187-.156-.426-.199-.688-.199a.559.559 0 0 1-.254-.078.253.253 0 0 1-.114-.358c.028-.054.16-.186.192-.21.356-.202.767-.136 1.146.016.352.144.618.408 1.001.782.391.451.462.576.685.914.176.265.336.537.445.848.067.195-.019.354-.25.452z"
      />
    </svg>
  );

  const models = {
    deepseek: {
      icon: deepseekIcon,
      models: [
        { name: "Deepseek Chat", id: "deepseek-chat" },
        { name: "Deepseek Reasoner", id: "deepseek-reasoner" },
      ],
    },
  };

  const navigate = useNavigate();
  const [selectedModel, setSelectedModel] = useState(null);
  const [keyboardShortcut, setKeyboardShortcut] = useState("Ctrl x2");
  const [isEditingShortcut, setIsEditingShortcut] = useState(false);
  const [lastSingleKeyPressTime, setLastSingleKeyPressTime] = useState(null);
  const [lastSingleKeyPressKey, setLastSingleKeyPressKey] = useState(null);
  const shortcutInputRef = useRef(null);

  useEffect(() => {
    const firstProviderKey = Object.keys(models)[0];
    if (firstProviderKey) {
      // @ts-ignore
      const firstModel = models[firstProviderKey].models[0];
      if (firstModel) setSelectedModel(firstModel.id);
    }
  }, []);

  const handleDoubleClickShortcut = () => {
    setIsEditingShortcut(true);
    setLastSingleKeyPressKey(null);
    setLastSingleKeyPressTime(null);
    // @ts-ignore
    requestAnimationFrame(() => shortcutInputRef.current?.focus());
  };

  const handleInputBlur = () => {
    setIsEditingShortcut(false);
    setLastSingleKeyPressKey(null);
    setLastSingleKeyPressTime(null);
  };

  const handleKeyDown = (event: any) => {
    event.preventDefault();
    event.stopPropagation();
    const { key, metaKey, ctrlKey, altKey, shiftKey } = event;
    if (key === "Escape") {
      setKeyboardShortcut("");
      setIsEditingShortcut(false);
      setLastSingleKeyPressKey(null);
      setLastSingleKeyPressTime(null);
      return;
    }

    // Build modifier list
    const modParts = [];
    if (metaKey) modParts.push("Cmd");
    if (ctrlKey) modParts.push("Ctrl");
    if (altKey) modParts.push("Alt");
    if (shiftKey) modParts.push("Shift");

    // Determine baseKey for non-modifier keys
    const isModifierKey = ["Control", "Shift", "Alt", "Meta"].includes(key);
    let baseKeyDisplay = null;
    if (!isModifierKey) {
      if (key === " ") baseKeyDisplay = "Space";
      else if (key.startsWith("Arrow"))
        // @ts-ignore
        baseKeyDisplay = {
          ArrowUp: "↑",
          ArrowDown: "↓",
          ArrowLeft: "←",
          ArrowRight: "→",
        }[key];
      else if (
        [
          "Enter",
          "Tab",
          "Delete",
          "PageUp",
          "PageDown",
          "Home",
          "End",
        ].includes(key)
      )
        // @ts-ignore
        baseKeyDisplay = {
          Enter: "Enter",
          Tab: "Tab",
          Delete: "Del",
          PageUp: "PgUp",
          PageDown: "PgDown",
          Home: "Home",
          End: "End",
        }[key];
      else if (key.length === 1 || key.startsWith("F"))
        baseKeyDisplay = key.length === 1 ? key.toUpperCase() : key;
      else baseKeyDisplay = key;
    }

    const now = Date.now();
    const doublePressWindow = 400;

    // 1. Multi-key combos: modifiers + base key (e.g., Ctrl+Shift+A)
    if (modParts.length >= 1 && baseKeyDisplay) {
      setKeyboardShortcut([...modParts, baseKeyDisplay].join("+"));
      setIsEditingShortcut(false);
      setLastSingleKeyPressKey(null);
      setLastSingleKeyPressTime(null);
      return;
    }

    // 2. Pure modifier chords (e.g., Ctrl+Alt)
    // Update displayed shortcut but keep editing active
    if (modParts.length > 1 && !baseKeyDisplay && isModifierKey) {
      setKeyboardShortcut(modParts.join("+"));
      // Do NOT exit editing: wait for potential base key
      setLastSingleKeyPressKey(null);
      setLastSingleKeyPressTime(null);
      return;
    }

    // 3. Single or double key presses
    let currentPressCandidate = null;
    if (modParts.length === 1 && !baseKeyDisplay && isModifierKey) {
      currentPressCandidate = modParts[0];
    } else if (modParts.length === 0 && baseKeyDisplay) {
      currentPressCandidate = baseKeyDisplay;
    }

    if (currentPressCandidate) {
      if (
        lastSingleKeyPressKey === currentPressCandidate &&
        now - (lastSingleKeyPressTime || 0) < doublePressWindow
      ) {
        setKeyboardShortcut(`${currentPressCandidate} x2`);
        setIsEditingShortcut(false);
        setLastSingleKeyPressKey(null);
        setLastSingleKeyPressTime(null);
      } else {
        setKeyboardShortcut(currentPressCandidate);
        setLastSingleKeyPressKey(currentPressCandidate);
        // @ts-ignore
        setLastSingleKeyPressTime(now);
      }
    }
  };

  return (
    <div className=" flex flex-col h-screen">
      <Menu absolute={false} />
      <div className="flex flex-col gap-4 p-4 h-full overflow-auto">
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="rounded-full h-8 w-8"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ChevronLeft size={18} />
          </Button>
          <h1 className="text-2xl font-semibold">Settings</h1>
        </div>
        <Separator />
        <h2 className="text-xl font-bold">Appearance</h2>

        <ModeToggle />
        <Separator></Separator>
        <h2 className="text-xl font-bold">Model Configuration</h2>
        <h1 className="text-sm font-semibold -mb-2">Provider</h1>
        {/* @ts-ignore */}
        <Select value={selectedModel || ""} onValueChange={setSelectedModel}>
          <SelectTrigger className="w-[280px]">
            {selectedModel ? (
              <div className="flex items-center gap-2">
                {
                  Object.values(models).find((p) =>
                    p.models.some((m) => m.id === selectedModel),
                  )?.icon
                }
                {
                  Object.values(models)
                    .flatMap((p) => p.models)
                    .find((m) => m.id === selectedModel)?.name
                }
              </div>
            ) : (
              "Select a model"
            )}
          </SelectTrigger>
          <SelectContent>
            {Object.entries(models).map(([providerName, providerData]) => (
              <SelectGroup key={providerName}>
                <SelectLabel className="capitalize text-xs">
                  {providerName}
                </SelectLabel>
                {providerData.models.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex items-center gap-2">
                      {providerData.icon}
                      {model.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ))}
          </SelectContent>
        </Select>
        <h1 className="text-sm font-semibold -mb-2">Key</h1>
        <Input type="password" placeholder="sk-..." className="max-w-2xl" />
        <p className="text-sm text-muted-foreground mt-2">
          Your API key is stored locally and not shared.
        </p>
        <Separator className="my-4" />
        <h2 className="text-xl font-bold">Keyboard Shortcut</h2>
        <div className="flex flex-col gap-2">
          <div
            className={cn(
              "flex h-10 max-w-2xl rounded-md border border-input bg-background text-sm ring-offset-background",
              "cursor-pointer items-center overflow-hidden px-3 py-2 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
            )}
            onDoubleClick={handleDoubleClickShortcut}
          >
            {!isEditingShortcut ? (
              keyboardShortcut ? (
                keyboardShortcut.endsWith(" x2") ? (
                  <div className="inline-flex items-center">
                    <kbd className="select-none rounded bg-muted px-2 py-1 font-mono text-sm font-medium">
                      {keyboardShortcut.replace(" x2", "")}
                    </kbd>
                    <span className="ml-1 text-sm text-muted-foreground/80">
                      x2
                    </span>
                  </div>
                ) : (
                  <div className="inline-flex items-center gap-1">
                    {keyboardShortcut.split("+").map((key, i, arr) => (
                      <React.Fragment key={i}>
                        <kbd className="select-none rounded border px-2 py-0.5 font-mono text-sm font-medium">
                          {key}
                        </kbd>
                        {i < arr.length - 1 && (
                          <span className="text-sm text-foreground/80">+</span>
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                )
              ) : (
                <span className="text-muted-foreground italic">
                  No shortcut set. Double-click here to set.
                </span>
              )
            ) : (
              <Input
                ref={shortcutInputRef}
                type="text"
                readOnly
                value={keyboardShortcut}
                onKeyDown={handleKeyDown}
                onBlur={handleInputBlur}
                placeholder="Press shortcut keys here..."
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer font-mono"
                autoComplete="off"
                spellCheck="false"
              />
            )}
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Double-click to edit. Press keys to set. Escape to clear. Single
            key/modifier can be pressed twice for x2. Multi-key combos finalize
            immediately.
          </p>
        </div>
      </div>
    </div>
  );
}



import { Textarea } from "@/components/ui/textarea";

import { Button } from "./components/ui/button";
import { ArrowUp, Command, Folder, SettingsIcon, X } from "lucide-react";
import React, { useState, useRef, useEffect, Fragment } from "react";
import { SelectFolder, GetLastFolderPath } from "../wailsjs/go/main/App";
import { WindowHide as Quit, EventsOn } from "../wailsjs/runtime/runtime";
import { cn } from "./lib/utils";
import { HashRouter, Link, Route, Routes, useNavigate } from "react-router-dom";
import { Separator } from "./components/ui/separator";
import { ChevronLeft } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
} from "./components/ui/select";

function Menu({ absolute }: { absolute: boolean }) {
  if (!absolute) {
    return (
      <div className="flex flex-row border-b-muted border-b p-4 justify-between w-full items-center">
        <h1 className="text-xl items-center select-none font-bold flex flex-row gap-2">
          <Command />
          <span>Control</span>
        </h1>
        <Button variant="ghost" size="icon" onClick={() => Quit()}>
          <X size={16} />
        </Button>
      </div>
    );
  } else {
    return (
      <Fragment>
        <h1 className="text-xl items-center select-none absolute font-bold  top-[1.4rem] left-4 flex flex-row gap-2">
          <Command />
          <span>Control</span>
        </h1>
        <Button
          variant="ghost"
          className="absolute top-3 right-4"
          size="icon"
          onClick={() => Quit()}
        >
          <X size={16} />
        </Button>
      </Fragment>
    );
  }
}

import { Input } from "@/components/ui/input";
import { ThemeProvider } from "./components/theme-provider";
import { ModeToggle } from "./components/mode-toggle";

export function Settings() {
  const deepseekIcon = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      style={{ flex: "none", lineHeight: "1" }} // Corrected style prop format
      viewBox="0 0 24 24"
      width="18" // Add width/height for better rendering control
      height="18"
    >
      <path
        fill="#4D6BFE"
        d="M23.748 4.482c-.254-.124-.364.113-.512.234-.051.039-.094.09-.137.136-.372.397-.806.657-1.373.626-.829-.046-1.537.214-2.163.848-.133-.782-.575-1.248-1.247-1.548-.352-.156-.708-.311-.955-.65-.172-.241-.219-.51-.305-.774-.055-.16-.11-.323-.293-.35-.2-.031-.278.136-.356.276-.313.572-.434 1.202-.422 1.84.027 1.436.633 2.58 1.838 3.393.137.093.172.187.129.323-.082.28-.18.552-.266.833-.055.179-.137.217-.329.14a5.526 5.526 0 0 1-1.736-1.18c-.857-.828-1.631-1.742-2.597-2.458a11.365 11.365 0 0 0-.689-.471c-.985-.957.13-1.743.388-1.836.27-.098.093-.432-.779-.428-.872.004-1.67.295-2.687.684a3.055 3.055 0 0 1-.465.137 9.597 9.597 0 0 0-2.883-.102c-1.885.21-3.39 1.102-4.497 2.623C.082 8.606-.231 10.684.152 12.85c.403 2.284 1.569 4.175 3.36 5.653 1.858 1.533 3.997 2.284 6.438 2.14 1.482-.085 3.133-.284 4.994-1.86.47.234.962.327 1.78.397.63.059 1.236-.03 1.705-.128.735-.156.684-.837.419-.961-2.155-1.004-1.682-.595-2.113-.926 1.096-1.296 2.746-2.642 3.392-7.003.05-.347.007-.565 0-.845-.004-.17.035-.237.23-.256a4.173 4.173 0 0 0 1.545-.475c1.396-.763 1.96-2.015 2.093-3.517.02-.23-.004-.467-.247-.588zM11.581 18c-2.089-1.642-3.102-2.183-3.52-2.16-.392.024-.321.471-.235.763.09.288.207.486.371.739.114.167.192.416-.113.603-.673.416-1.842-.14-1.897-.167-1.361-.802-2.5-1.86-3.301-3.307-.774-1.393-1.224-2.887-1.298-4.482-.02-.386.093-.522.477-.592a4.696 4.696 0 0 1 1.529-.039c2.132.312 3.946 1.265 5.468 2.774.868.86 1.525 1.887 2.202 2.891.72 1.066 1.494 2.082 2.48 2.914.348.292.625.514.891.677-.802.09-2.14.11-3.054-.614zm1-6.44a.306.306 0 0 1 .415-.287.302.302 0 0 1 .2.288.306.306 0 0 1-.31.307.303.303 0 0 1-.304-.308zm3.11 1.596c-.2.081-.399.151-.59.16a1.245 1.245 0 0 1-.798-.254c-.274-.23-.47-.358-.552-.758a1.73 1.73 0 0 1 .016-.588c.07-.327-.008-.537-.239-.727-.187-.156-.426-.199-.688-.199a.559.559 0 0 1-.254-.078.253.253 0 0 1-.114-.358c.028-.054.16-.186.192-.21.356-.202.767-.136 1.146.016.352.144.618.408 1.001.782.391.451.462.576.685.914.176.265.336.537.445.848.067.195-.019.354-.25.452z"
      />
    </svg>
  );

  const models = {
    deepseek: {
      icon: deepseekIcon,
      models: [
        { name: "Deepseek Chat", id: "deepseek-chat" },
        { name: "Deepseek Reasoner", id: "deepseek-reasoner" },
      ],
    },
  };

  const navigate = useNavigate();
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  // Define types for Go integration
  interface GoWindow extends Window {
    go: {
      main: {
        App: {
          GetConfig: () => Promise<AppConfig>;
          UpdateShowWindowShortcut: (shortcut: ShortcutKey) => Promise<void>;
        }
      }
    }
  }

  // Define ShortcutEvent type
  interface ShortcutEvent {
    ctrl: boolean;
    shift: boolean;
    alt: boolean;
    meta: boolean;
    key: string;
  }

  // Define ShortcutKey type for Go integration
  interface ShortcutKey {
    type: string;
    key: string;
    ctrl: boolean;
    shift: boolean;
    alt: boolean;
    meta: boolean;
    sequence?: KeySeq[];
  }

  // Define KeySeq type for Go integration
  interface KeySeq {
    key: string;
    ctrl: boolean;
    shift: boolean;
    alt: boolean;
    meta: boolean;
  }

  // Define AppConfig type for Go integration
  interface AppConfig {
    showWindowShortcut: ShortcutKey;
  }

  // Type assertion for window
  // const goWindow = window as unknown as GoWindow;

  // State for keyboard shortcut capture
  const [capturedShortcut, setCapturedShortcut] = useState<
    | null
    | { type: "single"; event: ShortcutEvent }
    | { type: "double"; sequence: [ShortcutEvent, ShortcutEvent] }
  >(null);
  const [isListeningForShortcut, setIsListeningForShortcut] = useState(false);
  const [awaitingSecondPress, setAwaitingSecondPress] = useState(false);
  const [firstPressEvent, setFirstPressEvent] = useState<ShortcutEvent | null>(
    null,
  );
  const doublePressTimeoutId = useRef<number | null>(null); // Use number for browser timeout ID
  const shortcutRef = useRef<HTMLDivElement>(null); // Ref for the clickable area

  const DOUBLE_PRESS_DELAY = 500; // Milliseconds to wait for the second press

  // Effect to focus the div when listening starts and cleanup timeout
  useEffect(() => {
    if (isListeningForShortcut && shortcutRef.current) {
      // Use a small delay to ensure the element is ready for focus after state change
      const timeoutId = setTimeout(() => {
        shortcutRef.current?.focus();
      }, 0); // 0ms delay runs after paint

      // Reset temp state when starting listening (if not already)
      setAwaitingSecondPress(false);
      setFirstPressEvent(null);
      if (doublePressTimeoutId.current !== null) {
        clearTimeout(doublePressTimeoutId.current);
        doublePressTimeoutId.current = null;
      }

      return () => clearTimeout(timeoutId); // Cleanup focus timeout
    }
    // Cleanup double press timeout when listening stops or component unmounts
    return () => {
      if (doublePressTimeoutId.current !== null) {
        clearTimeout(doublePressTimeoutId.current);
        doublePressTimeoutId.current = null;
      }
    };
  }, [isListeningForShortcut]);

  // Helper to get parts of a single shortcut event for display
  const getShortcutParts = (event: ShortcutEvent): string[] => {
    const parts = [];
    if (event.ctrl) parts.push("Ctrl");
    if (event.shift) parts.push("Shift");
    if (event.alt) parts.push("Alt");
    if (event.meta) parts.push("Meta"); // Command key on Mac

    let mainKey = event.key;

    // Mapping for special keys and normalization
    const specialKeyMap: { [key: string]: string } = {
      " ": "Space",
      Escape: "Esc",
      ArrowUp: "↑",
      ArrowDown: "↓",
      ArrowLeft: "←",
      ArrowRight: "→",
      Enter: "Enter",
      Tab: "Tab",
      Backspace: "Backspace",
      Delete: "Del",
      Home: "Home",
      End: "End",
      PageUp: "Page Up",
      PageDown: "Page Down",
      PrintScreen: "PrtScr",
      Insert: "Ins",
      Pause: "Pause",
      ContextMenu: "Menu",
      CapsLock: "Caps",
      NumLock: "Num",
      ScrollLock: "Scroll",
      F1: "F1",
      F2: "F2",
      F3: "F3",
      F4: "F4",
      F5: "F5",
      F6: "F6",
      F7: "F7",
      F8: "F8",
      F9: "F9",
      F10: "F10",
      F11: "F11",
      F12: "F12",
      Control: "Ctrl",
      Meta: "Meta", // Command on Mac, Windows key on PC
      // Add more as needed
    };

    if (specialKeyMap[mainKey]) {
      mainKey = specialKeyMap[mainKey];
    } else if (mainKey.length === 1 && mainKey.match(/^[a-z0-9]$/i)) {
      // Capitalize letters and numbers if it's a single alphanumeric character
      mainKey = mainKey.toUpperCase();
    } else if (["Control", "Shift", "Alt", "Meta"].includes(mainKey)) {
      // If the key name is a modifier name, only include it if the corresponding modifier flag is NOT set,
      // which would indicate an unusual state, or if it's the only key pressed.
      // Simpler: just don't add the key name if it's a modifier name and the corresponding modifier is active.
      if (
        (mainKey === "Control" && event.ctrl) ||
        (mainKey === "Shift" && event.shift) ||
        (mainKey === "Alt" && event.alt) ||
        (mainKey === "Meta" && event.meta)
      ) {
        mainKey = ""; // Don't add e.key as a main key if it's just the modifier key itself
      }
    }
    // F-keys etc. are usually fine as is (e.g., "F1", "F12")

    // Add mainKey only if it's not empty after processing and not already in parts (handles Ctrl+Ctrl case correctly)
    if (mainKey && !parts.includes(mainKey)) {
      parts.push(mainKey);
    }

    return parts;
  };

  // Format the current state for display in the UI
  const formatShortcutDisplay = (): string => {
    if (isListeningForShortcut) {
      if (awaitingSecondPress && firstPressEvent) {
        const parts = getShortcutParts(firstPressEvent);
        if (parts.length === 0) return "Press shortcut..."; // Should not happen if firstPressEvent is set correctly
        return `${parts.join(" + ")} ...`;
      }
      return "Press shortcut...";
    } else {
      if (capturedShortcut === null) {
        return "Click to set";
      } else if (capturedShortcut.type === "single") {
        const parts = getShortcutParts(capturedShortcut.event);
        if (parts.length === 0) return "Invalid shortcut"; // Should not happen if capture logic is sound
        return parts.join(" + ");
      } else if (capturedShortcut.type === "double") {
        const parts1 = getShortcutParts(capturedShortcut.sequence[0]);
        const parts2 = getShortcutParts(capturedShortcut.sequence[1]);
        if (parts1.length === 0 || parts2.length === 0)
          return "Invalid shortcut"; // Should not happen
        return `${parts1.join(" + ")}, ${parts2.join(" + ")}`;
      }
      return "Invalid state"; // Fallback for unexpected state
    }
  };

  // Timeout handler for double press
  const handleDoublePressTimeout = () => {
    console.log("Double press timeout"); // Debugging
    if (isListeningForShortcut) {
      // Timeout occurred before the second press. Abandon the double press attempt.
      setAwaitingSecondPress(false);
      setFirstPressEvent(null);
      // The display will revert from "Key ..." back to "Press shortcut..." if no shortcut was set,
      // or back to the previously captured shortcut.
    }
    doublePressTimeoutId.current = null; // Clear the ref
  };

  // KeyDown Handler for capturing the shortcut
  const handleShortcutKeyDown = (e: React.KeyboardEvent) => {
    if (!isListeningForShortcut) return;

    // Escape cancels listening
    if (e.key === "Escape") {
      if (doublePressTimeoutId.current !== null) {
        clearTimeout(doublePressTimeoutId.current);
        doublePressTimeoutId.current = null;
      }
      setAwaitingSecondPress(false);
      setFirstPressEvent(null);
      setIsListeningForShortcut(false);
      shortcutRef.current?.blur();
      e.preventDefault(); // Prevent Escape from doing browser stuff
      e.stopPropagation();
      return;
    }

    // Prevent default browser actions and propagation for potential shortcut keys
    e.preventDefault();
    e.stopPropagation();

    const currentEvent: ShortcutEvent = {
      ctrl: e.ctrlKey,
      shift: e.shiftKey,
      alt: e.altKey,
      meta: e.metaKey,
      key: e.key, // Store the raw key
    };
    const currentParts = getShortcutParts(currentEvent);

    // Ignore key combinations that don't represent anything formatable (e.g. just pressing Fn key?)
    if (currentParts.length === 0) {
      console.log("Ignored key with no formatable parts:", e.key, currentEvent); // Debug
      return;
    }

    if (awaitingSecondPress && firstPressEvent) {
      // We are waiting for the second key press of a potential double press
      if (doublePressTimeoutId.current !== null) {
        clearTimeout(doublePressTimeoutId.current); // Clear the previous timeout
        doublePressTimeoutId.current = null; // Ensure ref is null
      }

      // Check if this is the second press of the SAME key with SAME modifiers
      const isSameKeyAndModifiers =
        currentEvent.key === firstPressEvent.key &&
        currentEvent.ctrl === firstPressEvent.ctrl &&
        currentEvent.shift === firstPressEvent.shift &&
        currentEvent.alt === firstPressEvent.alt &&
        currentEvent.meta === firstPressEvent.meta;

      if (isSameKeyAndModifiers) {
        // Valid Double Press!
        const newShortcut: ShortcutKey = {
          type: "double",
          key: firstPressEvent.key,
          ctrl: firstPressEvent.ctrl,
          shift: firstPressEvent.shift,
          alt: firstPressEvent.alt,
          meta: firstPressEvent.meta,
          sequence: [
            {
              key: firstPressEvent.key,
              ctrl: firstPressEvent.ctrl,
              shift: firstPressEvent.shift,
              alt: firstPressEvent.alt,
              meta: firstPressEvent.meta,
            },
            {
              key: currentEvent.key,
              ctrl: currentEvent.ctrl,
              shift: currentEvent.shift,
              alt: currentEvent.alt,
              meta: currentEvent.meta,
            }
          ]
        };

        setCapturedShortcut({
          type: "double",
          sequence: [firstPressEvent, currentEvent],
        });
        setAwaitingSecondPress(false);
        setFirstPressEvent(null);
        setIsListeningForShortcut(false); // Stop listening
        shortcutRef.current?.blur(); // Remove focus
        console.log("Captured Double:", firstPressEvent, currentEvent); // Debug

        // Save to config
        const goWindow = window as unknown as GoWindow;
        goWindow.go.main.App.UpdateShowWindowShortcut(newShortcut)
          .catch((err: Error) => {
            console.error("Failed to save shortcut:", err);
          });
      } else {
        // Not a double press of the first key. This new keydown is potentially the start of a NEW shortcut.
        console.log(
          "Not double of first key/modifiers. Processing as new potential start:",
          currentEvent,
        ); // Debug
        setAwaitingSecondPress(false); // Reset the double press state
        setFirstPressEvent(null); // Clear the old first event
        // Fall through to the logic for !awaitingSecondPress to process currentEvent as a new potential start.
        // This relies on the code structure processing the currentEvent below this block.
      }
    }

    // If not awaiting a second press OR the second press didn't match the first
    if (!awaitingSecondPress) {
      // Determine if this event should be captured as a SINGLE shortcut immediately,
      // or if it should be treated as the FIRST press of a DOUBLE shortcut.

      // A single shortcut is typically Modifier + Key, or some specific non-modifier keys alone (like Enter, Space, F-keys).
      // A double shortcut starts with a single modifier key press OR a single non-modifier key press.

      const isModifierOnly =
        currentParts.length === 1 &&
        ["Ctrl", "Shift", "Alt", "Meta"].includes(currentParts[0]);
      const isSingleKeyOnly = currentParts.length === 1 && !isModifierOnly;
      const isModifierPlusKey = currentParts.length > 1;

      if (isModifierPlusKey) {
        // It's a standard modifier + key combo (e.g. Ctrl+S, Shift+Enter). Capture as a Single Shortcut.
        const newShortcut: ShortcutKey = {
          type: "single",
          key: currentEvent.key,
          ctrl: currentEvent.ctrl,
          shift: currentEvent.shift,
          alt: currentEvent.alt,
          meta: currentEvent.meta,
        };

        setCapturedShortcut({ type: "single", event: currentEvent });
        setIsListeningForShortcut(false); // Stop listening
        shortcutRef.current?.blur(); // Remove focus
        console.log("Captured Single (Mod+Key):", currentEvent); // Debug

        // Save to config
        const goWindow = window as unknown as GoWindow;
        goWindow.go.main.App.UpdateShowWindowShortcut(newShortcut)
          .catch((err: Error) => {
            console.error("Failed to save shortcut:", err);
          });
      } else if (isModifierOnly || isSingleKeyOnly) {
        // It's a single modifier key (e.g. Ctrl) or a single non-modifier key (e.g. A, Enter).
        // Treat this as the FIRST press of a potential Double Shortcut.
        setFirstPressEvent(currentEvent);
        setAwaitingSecondPress(true);
        doublePressTimeoutId.current = window.setTimeout(
          handleDoublePressTimeout,
          DOUBLE_PRESS_DELAY,
        );
        console.log("Awaiting second press:", currentEvent); // Debug
      } else {
        // This case should ideally be caught by the initial currentParts.length === 0 check,
        // but keeping this else branch for safety/debugging.
        console.log("Ignored unhandled key combination:", currentEvent); // Debug
      }
    }
  };

  useEffect(() => {
    const firstProviderKey = Object.keys(models)[0];
    if (firstProviderKey) {
      // @ts-ignore // TODO: Fix type issue with models object structure
      const firstModel = models[firstProviderKey].models[0];
      if (firstModel) setSelectedModel(firstModel.id);
    }

    // Load saved shortcut from config
    const goWindow = window as unknown as GoWindow;
    goWindow.go.main.App.GetConfig().then((config: AppConfig) => {
      if (config && config.showWindowShortcut) {
        const shortcut = config.showWindowShortcut;

        if (shortcut.type === "single") {
          setCapturedShortcut({
            type: "single",
            event: {
              ctrl: shortcut.ctrl,
              shift: shortcut.shift,
              alt: shortcut.alt,
              meta: shortcut.meta,
              key: shortcut.key,
            },
          });
        } else if (shortcut.type === "double" && shortcut.sequence && shortcut.sequence.length === 2) {
          setCapturedShortcut({
            type: "double",
            sequence: [
              {
                ctrl: shortcut.sequence[0].ctrl,
                shift: shortcut.sequence[0].shift,
                alt: shortcut.sequence[0].alt,
                meta: shortcut.sequence[0].meta,
                key: shortcut.sequence[0].key,
              },
              {
                ctrl: shortcut.sequence[1].ctrl,
                shift: shortcut.sequence[1].shift,
                alt: shortcut.sequence[1].alt,
                meta: shortcut.sequence[1].meta,
                key: shortcut.sequence[1].key,
              },
            ],
          });
        }
      }
    }).catch((err: Error) => {
      console.error("Failed to load config:", err);
    });
  }, []);

  return (
    <div className=" flex flex-col h-screen">
      <Menu absolute={false} />
      <div className="flex flex-col gap-4 p-4 h-full overflow-auto">
        {/* Back button and title */}
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="rounded-full h-8 w-8"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ChevronLeft size={18} />
          </Button>
          <h1 className="text-2xl font-semibold">Settings</h1>
        </div>

        <Separator />

        {/* Appearance Section */}
        <h2 className="text-xl font-bold">Appearance</h2>
        <ModeToggle />
        <Separator />

        {/* Model Configuration Section */}
        <h2 className="text-xl font-bold">Model Configuration</h2>
        <h1 className="text-sm font-semibold -mb-2">Provider</h1>
        {/* @ts-ignore */}
        <Select value={selectedModel || ""} onValueChange={setSelectedModel}>
          <SelectTrigger className="w-[280px]">
            {selectedModel ? (
              <div className="flex items-center gap-2">
                {
                  Object.values(models).find((p) =>
                    p.models.some((m) => m.id === selectedModel),
                  )?.icon
                }
                {
                  Object.values(models)
                    .flatMap((p) => p.models)
                    .find((m) => m.id === selectedModel)?.name
                }
              </div>
            ) : (
              "Select a model"
            )}
          </SelectTrigger>
          <SelectContent>
            {Object.entries(models).map(([providerName, providerData]) => (
              <SelectGroup key={providerName}>
                <SelectLabel className="capitalize text-xs">
                  {providerName}
                </SelectLabel>
                {providerData.models.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex items-center gap-2">
                      {providerData.icon}
                      {model.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ))}
          </SelectContent>
        </Select>
        <h1 className="text-sm font-semibold -mb-2">Key</h1>
        <Input type="password" placeholder="sk-..." className="max-w-2xl" />
        <p className="text-sm text-muted-foreground mt-2">
          Your API key is stored locally and not shared.
        </p>

        <Separator />

        {/* Keyboard Shortcuts Section */}
        <h2 className="text-xl font-bold">Keyboard Shortcuts</h2>
        <div className="flex items-center justify-between max-w-2xl">
          <label htmlFor="show-hide-shortcut" className="text-sm font-semibold">
            Show Window
          </label>
          {/* Div to capture shortcut */}
          <div
            ref={shortcutRef}
            id="show-hide-shortcut"
            tabIndex={0} // Make it focusable
            className={cn(
              "w-[150px] h-9 flex items-center justify-center border border-input bg-background rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", // Mimic input styling
              isListeningForShortcut
                ? "border-primary ring-2 ring-primary/50 text-primary font-semibold"
                : "text-muted-foreground", // Indicate listening state
              "cursor-pointer", // Indicate it's interactive
            )}
            onClick={() => setIsListeningForShortcut(true)} // Start listening on click
            onBlur={() => {
              // Stop listening if blur occurs and we were actively listening
              console.log("Blur detected, stopping listening"); // Debug
              if (isListeningForShortcut) {
                // Clear any pending double press timeout
                if (doublePressTimeoutId.current !== null) {
                  clearTimeout(doublePressTimeoutId.current);
                  doublePressTimeoutId.current = null;
                }
                // Reset temp state for double press
                setAwaitingSecondPress(false);
                setFirstPressEvent(null);
                // Stop listening
                setIsListeningForShortcut(false);
                // Display will automatically update based on capturedShortcut state
              }
            }}
            onKeyDown={handleShortcutKeyDown} // Capture key presses
            // KeyUp is not strictly necessary for capturing, but might be useful for cleanup or edge cases
            onKeyUp={(e) => {
              // If Escape is pressed while listening, prevent its default keyup too, as we prevented keydown.
              if (isListeningForShortcut && e.key === "Escape")
                e.preventDefault();
            }}
          >
            {formatShortcutDisplay()} {/* Display the state */}
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Click the box and press the desired key combination. Supported formats:
          <ul className="list-disc pl-5 mt-1">
            <li>Single key (e.g., F12, Tab, Space)</li>
            <li>Modifier combinations (e.g., Ctrl+Shift+S, Alt+F4)</li>
            <li>Double-press of the same key (e.g., Tab,Tab or Ctrl,Ctrl) within {DOUBLE_PRESS_DELAY}ms</li>
            <li>Arrow keys (e.g., ↑, ↓, ←, →) and their combinations</li>
          </ul>
          Press Esc to cancel.
        </p>
      </div>
    </div>
  );
}

function App() {
  return (
    <HashRouter basename={"/"}>
      {/* Apply layout classes here */}
      <ThemeProvider defaultTheme="dark">
        <div className=" bg-background text-foreground rounded-[1.2rem] overflow-hidden min-h-screen shadow-[inset_0_0_0_2px_rgba(0,0,0,0.1)]">
          {/* The rest of your app goes here */}
          <Routes>
            <Route path="/" element={<AskPage />} />
            <Route path="/settings" element={<Settings />} />
            <Route
              path="*"
              element={
                <div className="flex flex-col items-center justify-center h-screen text-center p-8">
                  <div className="text-5xl font-bold text-destructive mb-4">
                    404 - Not Found
                  </div>
                  <Link
                    to="/"
                    className="text-primary mt-4 hover:underline text-lg"
                  >
                    Go Home
                  </Link>
                </div>
              }
            />
          </Routes>
        </div>
      </ThemeProvider>
    </HashRouter>
  );
}

function AskPage() {
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  useEffect(() => {
    async function loadLastFolder() {
      const path = await GetLastFolderPath();
      if (path && path !== "") {
        setSelectedFolder(path);
      }
    }
    loadLastFolder();

    // Add event listener for escape key
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        Quit();
      }
    };

    window.addEventListener("keydown", handleEscape);

    // Cleanup function to remove the event listener
    return () => {
      window.removeEventListener("keydown", handleEscape);
    };
  }, []); // Empty dependency array means this runs once after initial render

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []); // Empty dependency array means this runs once after initial render

  useEffect(() => {
    // Listen for the custom event from Go to focus the textarea
    const handleFocusTextarea = () => {
      // Add a small delay before focusing
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    };

    EventsOn("focusTextarea", handleFocusTextarea);
  }, [textareaRef]); // Depend on textareaRef

  const handleSelectFolder = async () => {
    try {
      const folderPath = await SelectFolder();
      setSelectedFolder(folderPath);
    } catch (error) {
      console.error("Error selecting folder:", error);
      setSelectedFolder("Error selecting folder");
    }
  };

  return (
    <div className="h-screen flex flex-col justify-center gap-4 p-8">
      <Menu absolute />
      <h1 className="mx-auto text-4xl mb-5 font-bold text-primary mt-10 select-none">
        What do you want to build?
      </h1>
      <div className="relative">
        <Textarea
          ref={textareaRef}
          placeholder="Ask anything, I can read, delete, and edit files, and I can also use the terminal for you."
          rows={5}
          className="pr-[120px] rounded-2xl pb-[40px]"
        />
        {/* Added padding for buttons */}
        <div className="absolute bottom-2 right-2 flex justify-between gap-2 w-full">
          {/* Positioned buttons */}
          <Button
            className={cn(
              "rounded-full w-fit gap-2 ml-4",
              !selectedFolder && "border-destructive",
            )}
            size={"sm"}
            variant={"outline"}
            onClick={handleSelectFolder}
          >
            <Folder size={16} />{" "}
            {selectedFolder ? `${selectedFolder}` : "Not Selected"}
          </Button>
          <Button size="icon" className="rounded-full gap-2 mr-1">
            <ArrowUp size={18} />
          </Button>
        </div>
      </div>
      <Link to="/settings" className="ml-auto rounded-full">
        <Button variant={"ghost"} className="w-fit rounded-full">
          <SettingsIcon size={16} className="mr-2" />
          Settings
        </Button>
      </Link>
    </div>
  );
}

export default App;
