package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	hook "github.com/robotn/gohook"
	"github.com/wailsapp/wails/v2/pkg/runtime"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
)

const (
	lastFolderPathFilename = "last_folder_path.txt"
	configFilename         = "config.json"
)

// Config represents the application configuration
type Config struct {
	Model           string            `json:"model"`
	Keys            map[string]string `json:"keys"`                        // Changed Key to Keys map
	OpenAIAPIURL    string            `json:"openai_api_url,omitempty"`    // Added field for OpenAI compatible API URL
	OpenAIModelName string            `json:"openai_model_name,omitempty"` // Added field for OpenAI compatible model name
}

// App struct
type App struct {
	ctx            context.Context
	lastFolderPath string
	config         Config
	isWindowOpen   bool
	isEscDisabled  bool
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		config: Config{ // Initialize the map here
			Keys: make(map[string]string),
		},
		isWindowOpen:  false,
		isEscDisabled: false,
	}
}

// GetConfigFile returns the path to the config file
func (a *App) GetConfigFile() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	// Create a hidden directory for application data
	appDataDir := filepath.Join(homeDir, ".control-app")
	return filepath.Join(appDataDir, configFilename), nil
}

// SaveConfig saves the current configuration to the config file
func (a *App) SaveConfig() error {
	configPath, err := a.GetConfigFile()
	if err != nil {
		return fmt.Errorf("failed to get config file path: %w", err)
	}

	// Ensure the directory exists
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// Marshal the config to JSON
	configJSON, err := json.MarshalIndent(a.config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config to JSON: %w", err)
	}

	// Write the config to the file
	err = os.WriteFile(configPath, configJSON, 0644)
	if err != nil {
		return fmt.Errorf("failed to write config to file %s: %w", configPath, err)
	}
	return nil
}

// LoadConfig loads the configuration from the config file
func (a *App) LoadConfig() error {
	configPath, err := a.GetConfigFile()
	if err != nil {
		return fmt.Errorf("failed to get config file path: %w", err)
	}

	// Check if file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// File does not exist, save the default config
		// Initialize Keys map before saving default config
		a.config = Config{Keys: make(map[string]string)}
		return a.SaveConfig()
	} else if err != nil {
		// Other error stat'ing file
		return fmt.Errorf("failed to stat file %s: %w", configPath, err)
	}

	// Read the config from the file
	configJSON, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("failed to read config from file %s: %w", configPath, err)
	}

	// Unmarshal the JSON to the config struct
	// Create a temporary struct to unmarshal into
	var tempConfig Config
	err = json.Unmarshal(configJSON, &tempConfig)
	if err != nil {
		// Handle potential errors from old config format
		var oldConfig struct {
			Model string `json:"model"`
			Key   string `json:"key"`
		}
		if unmarshalErr := json.Unmarshal(configJSON, &oldConfig); unmarshalErr == nil {
			// Successfully unmarshalled old format, convert to new format
			a.config = Config{
				Model:           oldConfig.Model,
				Keys:            make(map[string]string),
				OpenAIAPIURL:    "", // Initialize new field during migration
				OpenAIModelName: "", // Initialize new field during migration
			}
			// Attempt to extract provider from model ID if possible (e.g., "gemini:gemini-1.5-flash" -> "gemini")
			parts := strings.Split(oldConfig.Model, ":")
			if len(parts) > 0 {
				providerName := parts[0]
				a.config.Keys[providerName] = oldConfig.Key
			}
			fmt.Println("Migrated old config format to new format.")
			// Save the migrated config immediately
			if saveErr := a.SaveConfig(); saveErr != nil {
				fmt.Printf("Error saving migrated config: %s\n", saveErr)
			}
			return nil // Successfully loaded and migrated
		} else {
			// Neither new nor old format worked
			return fmt.Errorf("failed to unmarshal config from JSON (new or old format): %w", err)
		}
	}

	// Successfully unmarshalled new format
	a.config = tempConfig
	// Ensure Keys map is initialized even if it was null in the JSON
	if a.config.Keys == nil {
		a.config.Keys = make(map[string]string)
	}

	return nil
}

// GetConfig returns the current configuration
func (a *App) GetConfig() Config {
	// Always attempt to load config before returning
	if err := a.LoadConfig(); err != nil {
		fmt.Printf("Error loading config in GetConfig: %s\n", err)
		// Return current in-memory config, which might be default
	}
	return a.config
}

// startup is called at application startup
func (a *App) startup(ctx context.Context) {
	// Perform your setup here
	a.ctx = ctx

	// Load the last folder path
	folderPath, err := a.LoadLastFolderPath()
	if err != nil {
		// Log error, but don't prevent startup
		fmt.Printf("Error loading last folder path: %s\n", err)
	}
	a.lastFolderPath = folderPath

	// Load the configuration
	if err := a.LoadConfig(); err != nil {
		// Log error, but don't prevent startup
		fmt.Printf("Error loading config: %s\n", err)
	}
	// Ensure config.Keys is initialized after loading, even if the file was empty or had null Keys
	if a.config.Keys == nil {
		a.config.Keys = make(map[string]string)
	}

	// Start watching for double Ctrl
	go a.watchDoubleCtrl()
}

// domReady is called after front-end resources have been loaded
func (a App) domReady(ctx context.Context) {
	// Add your action here
}

// beforeClose is called when the application is about to quit,
// either by clicking the window close button or calling runtime.Quit.
// Returning true will cause the application to continue, false will continue shutdown as normal.
func (a *App) beforeClose(ctx context.Context) (prevent bool) {
	a.EmitFocusTextareaEvent()
	runtime.WindowHide(ctx)

	a.isWindowOpen = false

	return true
}

// shutdown is called at application termination
func (a *App) shutdown(ctx context.Context) {
	// Perform your teardown here
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

func (a *App) SelectFolder() (string, error) {
	a.isEscDisabled = true
	s, e := a.selectFolder()
	time.Sleep(200 * time.Millisecond)
	a.isEscDisabled = false
	return s, e
}
func (a *App) selectFolder() (string, error) {

	folderPath, err := runtime.OpenDirectoryDialog(a.ctx, runtime.OpenDialogOptions{
		Title:                "Select project folder",
		CanCreateDirectories: true,
	})
	if err != nil {
		return "", err
	}
	if folderPath != "" { // Only save if a folder was actually selected (user didn't cancel)
		if err := a.SaveLastFolderPath(folderPath); err != nil {
			// Log error, but don't return it as the folder selection was successful
			fmt.Printf("Error saving last folder path: %s\n", err)
		}

		a.lastFolderPath = folderPath
	}
	return folderPath, nil
}

func (a *App) GetLastFolderPath() string {
	return a.lastFolderPath
}

func (a *App) GetFolderPathFile() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	// Create a hidden directory for application data
	appDataDir := filepath.Join(homeDir, ".control-app")
	return filepath.Join(appDataDir, lastFolderPathFilename), nil
}

func (a *App) SaveLastFolderPath(path string) error {
	filePath, err := a.GetFolderPathFile()
	if err != nil {
		return fmt.Errorf("failed to get save file path: %w", err)
	}

	// Ensure the directory exists
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// Write the path to the file
	err = os.WriteFile(filePath, []byte(path), 0644)
	if err != nil {
		return fmt.Errorf("failed to write folder path to file %s: %w", filePath, err)
	}
	return nil
}

func (a *App) LoadLastFolderPath() (string, error) {
	filePath, err := a.GetFolderPathFile()
	if err != nil {
		return "", fmt.Errorf("failed to get load file path: %w", err)
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		// File does not exist, return empty string
		return "", nil
	} else if err != nil {
		// Other error stat'ing file
		return "", fmt.Errorf("failed to stat file %s: %w", filePath, err)
	}

	// Read the path from the file
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read folder path from file %s: %w", filePath, err)
	}

	return strings.TrimSpace(string(content)), nil
}

// EmitFocusTextareaEvent emits an event to the frontend to focus the textarea.
func (a *App) EmitFocusTextareaEvent() {
	runtime.EventsEmit(a.ctx, "focusTextarea")
}

// SetConfig updates the application configuration and saves it.
func (a *App) SetConfig(config Config) error { // Changed parameter type to Config
	a.config = config // Update the in-memory config
	// Ensure Keys map is initialized before saving
	if a.config.Keys == nil {
		a.config.Keys = make(map[string]string)
	}
	return a.SaveConfig() // Save the updated config to file
}

func (a *App) watchDoubleCtrl() {
	var last time.Time
	// map of Ctrl rawcodes: 29 = Left-Ctrl, 65508 = Right-Ctrl (your machine)
	ctrlRaw := map[int]bool{
		29:    true,
		65508: true,
	}

	hook.Register(hook.KeyDown, []string{"esc"}, func(e hook.Event) {
		if a.isWindowOpen && !a.isEscDisabled {
			runtime.WindowHide(a.ctx)
			a.isWindowOpen = false
		}
	})

	// Listen to every keydown, but only act on our Ctrl keys
	hook.Register(hook.KeyDown, nil, func(e hook.Event) {
		if !ctrlRaw[int(e.Rawcode)] {
			return
		}

		now := time.Now()
		if now.Sub(last) < 300*time.Millisecond {
			if !a.isWindowOpen {
				a.isWindowOpen = true
				runtime.WindowShow(a.ctx)
				runtime.WindowCenter(a.ctx)
				a.EmitFocusTextareaEvent() // Emit event to focus textarea
			}
		}
		last = now
	})

	s := hook.Start()
	<-hook.Process(s)
}

// getAppDataDir returns the path to the application data directory
func (a *App) getAppDataDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	// Create a hidden directory for application data
	return filepath.Join(homeDir, ".control-app"), nil
}

// Part represents a part of a message
type Part struct {
	Type    string   `json:"type"`
	Content string   `json:"content,omitempty"`
	Name    string   `json:"name,omitempty"`
	Args    []string `json:"args,omitempty"`
}

// HistoryEntry represents a single entry in the conversation history
type HistoryEntry struct {
	Role      string    `json:"role"`
	Parts     []Part    `json:"parts"`
	CreatedAt time.Time `json:"createdAt"`
}

// Convo represents the conversation structure
type Convo struct {
	WorkingDir string         `json:"working_dir"`
	History    []HistoryEntry `json:"history"`
}

// CreateNewConvo creates a new conversation file with the initial message
func (a *App) CreateNewConvo(initialMsg string) (string, error) {
	appDataDir, err := a.getAppDataDir()
	if err != nil {
		return "", fmt.Errorf("failed to get app data directory: %w", err)
	}

	// Generate a unique conversation ID (using timestamp for simplicity)
	convoID := fmt.Sprintf("%d", time.Now().UnixNano()) // More precise than Unix()

	// Construct the conversation directory path
	convoDir := filepath.Join(appDataDir, "convos", convoID)

	// Ensure the conversation directory exists
	if err := os.MkdirAll(convoDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create conversation directory %s: %w", convoDir, err)
	}

	// Construct the conversation file path
	convoFilePath := filepath.Join(convoDir, "convo.json")
	history := []HistoryEntry{}

	if initialMsg != "" {

		history = append(history, HistoryEntry{
			Role: "user",
			Parts: []Part{
				{Type: "user", Content: initialMsg},
			},
		})
	}
	// Create the initial conversation data structure
	convoData := Convo{
		WorkingDir: a.lastFolderPath,
		History:    history,
	}

	// Marshal the data to JSON
	convoJSON, err := json.MarshalIndent(convoData, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal conversation data to JSON: %w", err)
	}

	// Write the JSON to the file
	err = os.WriteFile(convoFilePath, convoJSON, 0644)
	if err != nil {
		return "", fmt.Errorf("failed to write conversation file %s: %w", convoFilePath, err)
	}

	return convoID, nil // Return the generated ID
}

func (a *App) GetConvo(id string) (Convo, error) {
	appDataDir, err := a.getAppDataDir()
	if err != nil {
		return Convo{}, fmt.Errorf("failed to get app data directory: %w", err)
	}

	// Construct the conversation file path
	convoFilePath := filepath.Join(appDataDir, "convos", id, "convo.json")

	// Check if the conversation file exists
	if _, err := os.Stat(convoFilePath); os.IsNotExist(err) {
		return Convo{}, fmt.Errorf("conversation file not found for ID %s: %w", id, err)
	} else if err != nil {
		return Convo{}, fmt.Errorf("failed to stat conversation file %s: %w", convoFilePath, err)
	}

	// Read the file content
	convoJSON, err := os.ReadFile(convoFilePath)
	if err != nil {
		return Convo{}, fmt.Errorf("failed to read conversation file %s: %w", convoFilePath, err)
	}

	// Unmarshal the JSON into a Convo struct
	var convoData Convo
	err = json.Unmarshal(convoJSON, &convoData)
	if err != nil {
		return Convo{}, fmt.Errorf("failed to unmarshal conversation data from file %s: %w", convoFilePath, err)
	}

	return convoData, nil
}
func (a *App) EnableEsc() {
	a.isEscDisabled = false
}

func (a *App) DisableEsc() {
	a.isEscDisabled = true
}

func (a *App) getClient() (openai.Client, error) {
	// Load config to get API key
	if err := a.LoadConfig(); err != nil {
		return openai.Client{}, fmt.Errorf("failed to load config: %w", err)
	}

	// Check if we have an API key configured
	var apiKey string
	if a.config.OpenAIAPIURL != "" {
		// Use custom OpenAI-compatible API
		if key, exists := a.config.Keys["openai"]; exists {
			apiKey = key
		} else {
			return openai.Client{}, fmt.Errorf("no OpenAI API key configured")
		}
		client := openai.NewClient(option.WithBaseURL(a.config.OpenAIAPIURL), option.WithAPIKey(apiKey))
		return client, nil
	} else {
		// Use default Gemini API (for Google AI Studio)
		if key, exists := a.config.Keys["gemini"]; exists {
			apiKey = key
		} else {
			return openai.Client{}, fmt.Errorf("no Gemini API key configured")
		}
		client := openai.NewClient(option.WithBaseURL("https://generativelanguage.googleapis.com/v1beta/openai/"), option.WithAPIKey(apiKey))
		return client, nil
	}
}

// AddMessageToConvo adds a new message to an existing conversation
func (a *App) AddMessageToConvo(convoID string, role string, content string) error {
	appDataDir, err := a.getAppDataDir()
	if err != nil {
		return fmt.Errorf("failed to get app data directory: %w", err)
	}

	// Construct the conversation file path
	convoFilePath := filepath.Join(appDataDir, "convos", convoID, "convo.json")

	// Load existing conversation
	convo, err := a.GetConvo(convoID)
	if err != nil {
		return fmt.Errorf("failed to get conversation: %w", err)
	}

	// Add new message
	newEntry := HistoryEntry{
		Role: role,
		Parts: []Part{
			{
				Type:    "text",
				Content: content,
			},
		},
		CreatedAt: time.Now(),
	}
	convo.History = append(convo.History, newEntry)

	// Save updated conversation
	convoJSON, err := json.MarshalIndent(convo, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal conversation data to JSON: %w", err)
	}

	err = os.WriteFile(convoFilePath, convoJSON, 0644)
	if err != nil {
		return fmt.Errorf("failed to write conversation file %s: %w", convoFilePath, err)
	}

	return nil
}

// SendMessage sends a message to the AI and returns the response
func (a *App) SendMessage(convoID string, content string) (string, error) {
	// Validate configuration first
	if err := a.ValidateConfig(); err != nil {
		return "", err
	}

	// Add user message to conversation
	err := a.AddMessageToConvo(convoID, "user", content)
	if err != nil {
		return "", fmt.Errorf("failed to add user message: %w", err)
	}

	// Get updated conversation
	convo, err := a.GetConvo(convoID)
	if err != nil {
		return "", fmt.Errorf("failed to get conversation: %w", err)
	}

	// Convert conversation history to OpenAI format
	var messages []openai.ChatCompletionMessageParamUnion
	for _, entry := range convo.History {
		var content string
		for _, part := range entry.Parts {
			if part.Type == "text" {
				content += part.Content
			}
		}

		if entry.Role == "user" {
			messages = append(messages, openai.UserMessage(content))
		} else if entry.Role == "assistant" {
			messages = append(messages, openai.AssistantMessage(content))
		}
	}

	// Get OpenAI client
	client, err := a.getClient()
	if err != nil {
		return "", fmt.Errorf("failed to get AI client: %w", err)
	}

	// Determine model to use
	model := "gemini-1.5-flash"
	if a.config.OpenAIModelName != "" {
		model = a.config.OpenAIModelName
	} else if a.config.Model != "" {
		// Extract model from config.Model (e.g., "gemini:gemini-1.5-flash" -> "gemini-1.5-flash")
		parts := strings.Split(a.config.Model, ":")
		if len(parts) > 1 {
			model = parts[1]
		}
	}

	// Send request to OpenAI
	chatCompletion, err := client.Chat.Completions.New(context.Background(), openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    model,
	})
	if err != nil {
		return "", fmt.Errorf("failed to get AI response: %w", err)
	}

	// Extract response content
	var responseContent string
	if len(chatCompletion.Choices) > 0 {
		responseContent = chatCompletion.Choices[0].Message.Content
	} else {
		return "", fmt.Errorf("no response from AI")
	}

	// Add assistant response to conversation
	err = a.AddMessageToConvo(convoID, "assistant", responseContent)
	if err != nil {
		return "", fmt.Errorf("failed to add assistant message: %w", err)
	}

	return responseContent, nil
}

// SendMessageStream sends a message to the AI and streams the response
func (a *App) SendMessageStream(convoID string, content string) error {
	// Validate configuration first
	if err := a.ValidateConfig(); err != nil {
		runtime.EventsEmit(a.ctx, "stream_error", map[string]interface{}{
			"convoID": convoID,
			"error":   err.Error(),
		})
		return err
	}

	// Add user message to conversation
	err := a.AddMessageToConvo(convoID, "user", content)
	if err != nil {
		runtime.EventsEmit(a.ctx, "stream_error", map[string]interface{}{
			"convoID": convoID,
			"error":   fmt.Sprintf("failed to add user message: %v", err),
		})
		return fmt.Errorf("failed to add user message: %w", err)
	}

	// Emit user message added event
	runtime.EventsEmit(a.ctx, "message_added", map[string]interface{}{
		"convoID": convoID,
		"role":    "user",
		"content": content,
	})

	// Get updated conversation
	convo, err := a.GetConvo(convoID)
	if err != nil {
		runtime.EventsEmit(a.ctx, "stream_error", map[string]interface{}{
			"convoID": convoID,
			"error":   fmt.Sprintf("failed to get conversation: %v", err),
		})
		return fmt.Errorf("failed to get conversation: %w", err)
	}

	// Convert conversation history to OpenAI format
	var messages []openai.ChatCompletionMessageParamUnion
	for _, entry := range convo.History {
		var content string
		for _, part := range entry.Parts {
			if part.Type == "text" {
				content += part.Content
			}
		}

		if entry.Role == "user" {
			messages = append(messages, openai.UserMessage(content))
		} else if entry.Role == "assistant" {
			messages = append(messages, openai.AssistantMessage(content))
		}
	}

	// Get OpenAI client
	client, err := a.getClient()
	if err != nil {
		runtime.EventsEmit(a.ctx, "stream_error", map[string]interface{}{
			"convoID": convoID,
			"error":   fmt.Sprintf("failed to get AI client: %v", err),
		})
		return fmt.Errorf("failed to get AI client: %w", err)
	}

	// Determine model to use
	model := "gemini-1.5-flash"
	if a.config.OpenAIModelName != "" {
		model = a.config.OpenAIModelName
	} else if a.config.Model != "" {
		// Extract model from config.Model (e.g., "gemini:gemini-1.5-flash" -> "gemini-1.5-flash")
		parts := strings.Split(a.config.Model, ":")
		if len(parts) > 1 {
			model = parts[1]
		}
	}

	// Start streaming response
	runtime.EventsEmit(a.ctx, "stream_start", map[string]interface{}{
		"convoID": convoID,
	})

	stream := client.Chat.Completions.NewStreaming(context.Background(), openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    model,
	})
	acc := openai.ChatCompletionAccumulator{}
	responseContent := ""
	for stream.Next() {
		chunk := stream.Current()
		acc.AddChunk(chunk)

		// if using tool calls
		if tool, ok := acc.JustFinishedToolCall(); ok {
			println("Tool call stream finished:", tool.Index, tool.Name, tool.Arguments)
		}

		if refusal, ok := acc.JustFinishedRefusal(); ok {
			println("Refusal stream finished:", refusal)
		}

		// it's best to use chunks after handling JustFinished events
		if len(chunk.Choices) > 0 {
			responseContent += chunk.Choices[0].Delta.Content
			runtime.EventsEmit(a.ctx, "stream_chunk", map[string]interface{}{
				"convoID": convoID,
				"content": chunk.Choices[0].Delta.Content,
			})
		}
	}
	if err != nil {
		runtime.EventsEmit(a.ctx, "stream_error", map[string]interface{}{
			"convoID": convoID,
			"error":   fmt.Sprintf("failed to get AI response: %v", err),
		})
		return fmt.Errorf("failed to get AI response: %w", err)
	}

	// Add assistant response to conversation
	err = a.AddMessageToConvo(convoID, "assistant", responseContent)
	if err != nil {
		runtime.EventsEmit(a.ctx, "stream_error", map[string]interface{}{
			"convoID": convoID,
			"error":   fmt.Sprintf("failed to add assistant message: %v", err),
		})
		return fmt.Errorf("failed to add assistant message: %w", err)
	}

	// Emit a final complete event after saving the message
	runtime.EventsEmit(a.ctx, "stream_complete", map[string]any{
		"convoID": convoID,
		"content": responseContent,
	})
	return nil
}

// ValidateConfig checks if the configuration has the necessary API keys
func (a *App) ValidateConfig() error {
	if err := a.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	if a.config.OpenAIAPIURL != "" {
		// Custom OpenAI-compatible API
		if _, exists := a.config.Keys["openai"]; !exists {
			return fmt.Errorf("OpenAI API key not configured. Please go to Settings and add your OpenAI API key.")
		}
	} else {
		// Default Gemini API
		if _, exists := a.config.Keys["gemini"]; !exists {
			return fmt.Errorf("Gemini API key not configured. Please go to Settings and add your Gemini API key.")
		}
	}

	return nil
}

// ConvoInfo represents basic information about a conversation
type ConvoInfo struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	LastMessage string `json:"last_message"`
	CreatedAt   int64  `json:"created_at"`
}

// ListConversations returns a list of all conversations
func (a *App) ListConversations() ([]ConvoInfo, error) {
	appDataDir, err := a.getAppDataDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get app data directory: %w", err)
	}

	convosDir := filepath.Join(appDataDir, "convos")

	// Check if convos directory exists
	if _, err := os.Stat(convosDir); os.IsNotExist(err) {
		// No conversations directory exists yet
		return []ConvoInfo{}, nil
	} else if err != nil {
		return nil, fmt.Errorf("failed to stat convos directory: %w", err)
	}

	// Read the convos directory
	entries, err := os.ReadDir(convosDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read convos directory: %w", err)
	}

	var conversations []ConvoInfo
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		convoID := entry.Name()
		convoFilePath := filepath.Join(convosDir, convoID, "convo.json")

		// Check if conversation file exists
		if _, err := os.Stat(convoFilePath); os.IsNotExist(err) {
			continue
		}

		// Load conversation to get title and last message
		convo, err := a.GetConvo(convoID)
		if err != nil {
			// Skip this conversation if we can't load it
			continue
		}

		// Extract title from first user message (first 50 chars)
		title := "New Conversation"
		lastMessage := ""
		if len(convo.History) > 0 {
			for _, part := range convo.History[0].Parts {
				if part.Type == "text" && part.Content != "" {
					title = part.Content
					if len(title) > 50 {
						title = title[:50] + "..."
					}
					break
				}
			}

			// Get last message
			if len(convo.History) > 0 {
				lastEntry := convo.History[len(convo.History)-1]
				for _, part := range lastEntry.Parts {
					if part.Type == "text" && part.Content != "" {
						lastMessage = part.Content
						if len(lastMessage) > 100 {
							lastMessage = lastMessage[:100] + "..."
						}
						break
					}
				}
			}
		}

		// Get file info for creation time
		fileInfo, err := os.Stat(convoFilePath)
		var createdAt int64 = 0
		if err == nil {
			createdAt = fileInfo.ModTime().Unix()
		}

		conversations = append(conversations, ConvoInfo{
			ID:          convoID,
			Title:       title,
			LastMessage: lastMessage,
			CreatedAt:   createdAt,
		})
	}

	return conversations, nil
}
