{"name": "bruh", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.7", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.511.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.0", "zod": "^3.25.21"}, "devDependencies": {"@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.12", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "path": "^0.12.7", "postcss": "^8.4.17", "tailwindcss": "^4.1.7", "typescript": "^5.0.2", "url": "^0.11.1", "vite": "^6.3.5"}}