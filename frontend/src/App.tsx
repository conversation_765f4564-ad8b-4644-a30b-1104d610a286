import { <PERSON>h<PERSON><PERSON><PERSON>, <PERSON>, Route, Routes } from "react-router-dom";
import { Settings } from "./Settings";
import { Chat } from "./Chat";
import { ThemeProvider } from "./components/theme-provider";
import { AskPage } from "./AskPage";
import { cn } from "./lib/utils";
import { useEffect, useState } from "react";
import { WindowIsMaximised } from "../wailsjs/runtime/runtime";
import { SidebarProvider } from "./components/ui/sidebar";
import { ChatsSidebar } from "./components/chats-sidebar";
function App() {
  const [isMaximized, setIsMaximized] = useState(false);

  useEffect(() => {
    const checkMaximized = async () => {
      console.log("checking");
      try {
        const maximized = await WindowIsMaximised();
        setIsMaximized(maximized);
      } catch (error) {
        console.error("Error checking window maximized state:", error);
        // Handle the error appropriately (e.g., set a default value)
      }
    };

    // Check on mount
    checkMaximized();

    // Check on resize (or focus) as maximization state might change
    window.addEventListener("resize", checkMaximized);
    window.addEventListener("focus", checkMaximized);

    return () => {
      window.removeEventListener("resize", checkMaximized);
      window.removeEventListener("focus", checkMaximized);
    };
  }, []);
  return (
    <HashRouter basename={"/"}>
      {/* Apply layout classes here */}
      <ThemeProvider defaultTheme="dark">
        <div
          className={cn(
            "bg-background w-screen min-h-screen h-full text-foreground shadow-[inset_0_0_0_2px_rgba(0,0,0,0.1)] dark:shadow-[inset_0_0_0_2px_rgba(255,255,255,0.1)]",
            isMaximized ? "" : "rounded-[1.2rem]",
          )}
        >
          <SidebarProvider className="">
            <ChatsSidebar />
            {/* The rest of your app goes here */}
            <Routes>
              <Route path="/" element={<AskPage />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/chat/:id" element={<Chat />} />
              <Route
                path="*"
                element={
                  <div className="flex flex-col items-center justify-center h-screen text-center p-8">
                    <div className="text-5xl font-bold text-destructive mb-4">
                      404 - Not Found
                    </div>
                    <Link
                      to="/"
                      className="text-primary mt-4 hover:underline text-lg"
                    >
                      Go Home
                    </Link>
                  </div>
                }
              />
            </Routes>
          </SidebarProvider>
        </div>
      </ThemeProvider>
    </HashRouter>
  );
}

export default App;
