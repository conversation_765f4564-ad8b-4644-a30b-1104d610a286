import { Textarea } from "@/components/ui/textarea";

import { <PERSON><PERSON> } from "./components/ui/button";
import { <PERSON>U<PERSON>, Folder } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import {
  SelectFolder,
  GetLastFolderPath,
  CreateNewConvo,
} from "../wailsjs/go/main/App";
import { EventsOn } from "../wailsjs/runtime/runtime";
import { cn } from "./lib/utils";
import { Menu } from "./components/menu";
import { useNavigate } from "react-router-dom";

export function AskPage() {
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [text, setText] = useState("");

  // Function to handle submission (Ctrl+Enter or button click)
  const handleSubmit = async () => {
    if (!text.trim() || !selectedFolder) return;

    const message = text;
    // Create the conversation on the backend without the initial message.
    // The initial message will be sent by the Chat component via the URL parameter.
    const id = await CreateNewConvo(""); // Assuming CreateNewConvo can handle an empty initial message

    // Navigate with initial message as URL param for immediate streaming
    navigate(`/chat/${id}?initial=${encodeURIComponent(message)}`);
  };

  useEffect(() => {
    async function loadLastFolder() {
      const path = await GetLastFolderPath();
      if (path && path !== "") {
        setSelectedFolder(path);
      }
    }
    loadLastFolder();
  }, []); // Empty dependency array means this runs once after initial render

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [textareaRef]); // Empty dependency array means this runs once after initial render

  useEffect(() => {
    // uisten for the custom event from Go to focus the textarea
    const handleFocusTextarea = () => {
      // Add a small delay before focusing
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    };

    EventsOn("focusTextarea", handleFocusTextarea);
  }, [textareaRef]); // Depend on textareaRef

  const handleSelectFolder = async () => {
    try {
      const folderPath = await SelectFolder();
      setSelectedFolder(folderPath);
    } catch (error) {
      console.error("Error selecting folder:", error);
      setSelectedFolder("Error selecting folder");
    }
  };

  const navigate = useNavigate();

  return (
    <div className="h-screen w-full">
      <Menu />
      <div className="h-[85%] flex flex-col w-full justify-center gap-4 p-4">
        <h1 className="mx-auto text-3xl mb-2 font-bold text-primary select-none">
          What do you want to build?
        </h1>
        <div className="w-full flex items-center justify-center">
          <div className="relative w-full max-w-3xl">
            <Textarea
              ref={textareaRef}
              value={text}
              placeholder="Describe what you want to build. Control + Enter to submit."
              rows={3}
              onChange={(t) => setText(t.target.value)}
              className="rounded-xl pb-[40px]"
              onKeyDown={(event) => {
                // Check for Ctrl + Enter
                if (event.ctrlKey && event.key === "Enter") {
                  event.preventDefault(); // Prevent adding a newline
                  handleSubmit(); // Call the submit function
                }
              }}
            />
            {/* Added padding for buttons */}
            <div className="absolute bottom-2 right-2 flex justify-between gap-2 w-full">
              {/* Positioned buttons */}
              <Button
                className={cn(
                  "w-fit gap-2 ml-4",
                  !selectedFolder && "border-destructive",
                )}
                size={"sm"}
                variant={"outline"}
                onClick={handleSelectFolder}
              >
                <Folder size={16} />{" "}
                {selectedFolder ? `${selectedFolder}` : "Not Selected"}
              </Button>

              <Button
                size="icon"
                disabled={!selectedFolder || !text.trim()}
                onClick={handleSubmit}
                className=" gap-2 mr-1"
              >
                <ArrowUp />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
