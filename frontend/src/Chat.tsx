import { useParams, useSearchParams } from "react-router-dom";
import { Menu } from "./components/menu";
import React, { useEffect, useState, useRef, useCallback, useMemo } from "react";
import { GetConvo, SendMessageStream } from "../wailsjs/go/main/App";
import { EventsOn } from "../wailsjs/runtime/runtime";
import { Textarea } from "./components/ui/textarea";
import { Button } from "./components/ui/button";
import { ArrowUp, Copy, RefreshCw, Search } from "lucide-react";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism"; // You can choose a different style

import remarkGfm from "remark-gfm";
// Performance monitoring hook
const useStreamingPerformance = () => {
  const startTimeRef = useRef<number>(0);
  const chunkCountRef = useRef<number>(0);

  const startStreaming = useCallback(() => {
    startTimeRef.current = performance.now();
    chunkCountRef.current = 0;
  }, []);

  const addChunk = useCallback(() => {
    chunkCountRef.current += 1;
  }, []);

  const endStreaming = useCallback(() => {
    const duration = performance.now() - startTimeRef.current;
    const chunksPerSecond = chunkCountRef.current / (duration / 1000);
    console.log(`Streaming performance: ${chunkCountRef.current} chunks in ${duration.toFixed(2)}ms (${chunksPerSecond.toFixed(2)} chunks/sec)`);
  }, []);

  return { startStreaming, addChunk, endStreaming };
};

// Optimized message list hook
const useOptimizedMessageList = (history: HistoryEntry[]) => {
  return useMemo(() => {
    return history.map((entry, index) => {
      const previousEntry = history[index - 1];
      const userMessageTimestamp =
        previousEntry && previousEntry.role === "user"
          ? previousEntry.createdAt
          : undefined;

      return {
        entry,
        userMessageTimestamp,
        key: `${entry.createdAt}-${index}`,
        index,
      };
    });
  }, [history]);
};

// Helper function to format timestamp relatively
const formatRelativeTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = diffMs / 1000;
  const diffMin = diffSec / 60;
  const diffHrs = diffMin / 60;
  const diffDays = diffHrs / 24;

  if (diffSec < 60) {
    return "just now";
  } else if (diffMin < 60) {
    return `${Math.floor(diffMin)}m ago`;
  } else if (diffHrs < 24) {
    return `${Math.floor(diffHrs)}h ago`;
  } else if (diffDays < 7) {
    if (Math.floor(diffDays) === 1) {
      return "Yesterday";
    }
    return `${Math.floor(diffDays)}d ago`;
  } else {
    return date.toLocaleDateString(); // e.g., 11/10/2023
  }
};

export const NonTextPart = ({ part }: { part: Part }) => {
  const tools: any = {
    search: {
      icon: <Search />,
      text: (args: string[]) => `Searching for "${args[0]}"`,
    },
  };
  if (part.type == "toolCall") {
    const toolDef = tools[part.name];
    if (toolDef) {
      return (
        <Button
          variant={"outline"}
          className="rounded-full space-y-2"
          size="sm"
        >
          {toolDef.icon}
          {toolDef.text(part.args)}
        </Button>
      );
    } else {
      // Tool definition not found, use a generic message
      return (
        <Button
          variant={"outline"}
          className="rounded-full space-y-2"
          size="sm"
        >
          Ran tool {part.name}
        </Button>
      );
    }
  }
  // Should ideally return null or some fallback if not a toolCall
  return null;
};
export const UserChatBubble = React.memo(
  ({ entry }: { entry: HistoryEntry }) => {
    const { parts, createdAt } = entry;
    return (
      <div className="flex flex-col items-end">
        <div className="ml-auto w-fit max-w-xl p-3 rounded-t-2xl rounded-bl-2xl rounded-br bg-gray-50 shadow-sm border border-gray-300 text-gray-900 dark:bg-secondary/50 dark:border-muted dark:shadow-none dark:text-white">
          {parts.map((part, index) => (
            <div key={index}>
              {part.type === "text" ? (
                <p style={{ whiteSpace: "pre-wrap" }}>{part.content}</p>
              ) : (
                <p>{part.name}</p>
              )}
            </div>
          ))}
        </div>
        {/* Add timestamp */}
        <div className="text-right text-xs text-gray-500 dark:text-gray-400 mt-1 pr-1">
          <span title={new Date(createdAt).toLocaleString()}>
            {formatRelativeTime(createdAt)}
          </span>
        </div>
      </div>
    );
  },
);

import ReactMarkdown from "react-markdown";
import { ScrollArea } from "./components/ui/scroll-area";


// Memoized markdown components to prevent re-creation on every render
const markdownComponents = {
  hr: ({ node, ...props }: any) => (
    <hr className="my-4 border-t border-muted" {...props} />
  ),
  h1: ({ node, ...props }: any) => (
    <h1
      className="scroll-m-20 text-3xl pt-3 font-extrabold tracking-tight lg:text-5xl"
      {...props}
    />
  ),
  h2: ({ node, ...props }: any) => (
    <h2
      className="scroll-m-20 border-b pb-2 text-2xl font-semibold tracking-tight first:mt-0"
      {...props}
    />
  ),
  h3: ({ node, ...props }: any) => (
    <h3
      className="scroll-m-20 text-xl font-semibold tracking-tight"
      {...props}
    />
  ),
  h4: ({ node, ...props }: any) => (
    <h4
      className="scroll-m-20 text-lg font-semibold tracking-tight"
      {...props}
    />
  ),
  p: ({ node, ...props }: any) => (
    <p className="leading-7 [&:not(:first-child)]:mt-6" {...props} />
  ),
  blockquote: ({ node, ...props }: any) => (
    <blockquote className="mt-6 border-l-2 pl-6 italic" {...props} />
  ),
  ul: ({ node, ...props }: any) => (
    <ul className="my-6 ml-6 list-disc [&>li]:mt-2" {...props} />
  ),
  ol: ({ node, ...props }: any) => (
    <ol className="my-6 ml-6 list-decimal [&>li]:mt-2" {...props} />
  ),
  li: ({ node, ...props }: any) => <li className="mt-2" {...props} />,
  table: ({ node, ...props }: any) => (
    <div className="my-6 w-full overflow-hidden rounded-lg border ">
      <table
        // switch collapse off, zero spacing, plus rounding & full width
        className="w-full border-separate border-spacing-0 rounded-lg"
        {...props}
      />
    </div>
  ),
  tr: ({ node, ...props }: any) => (
    <tr
      className={`
         m-0 p-0
         hover:bg-muted transition-colors duration-200
         [&:nth-last-child(1)>td:first-child]:rounded-bl-lg
         [&:nth-last-child(1)>td:last-child]:rounded-br-lg
       `}
      {...props}
    />
  ),
  th: ({ node, ...props }: any) => (
    <th
      className={`
        border-[0.5px] px-4 py-2 text-left font-bold
        bg-muted/50
        [&:first-child]:rounded-tl-lg
        [&:last-child]:rounded-tr-lg
      `}
      {...props}
    />
  ),
  td: ({ node, ...props }: any) => (
    <td
      className={`
         border-[0.5px] px-4 py-2
       `}
      {...props}
    />
  ),
  code: React.memo(function CodeComponent({
    node,
    className,
    children,
    ...props
  }: any) {
    const match = /language-(\w+)/.exec(className || "");
    const inline = match == null;

    const codeContent = String(children).replace(/\n$/, "");

    // Handle inline code
    if (inline) {
      return (
        <code
          className="relative rounded-lg px-[0.3rem] border dark:border-2 shadow py-[0.2rem] font-mono text-sm font-semibold"
          {...props}
        >
          {codeContent}
        </code>
      );
    }

    // Handle code blocks
    const language = match ? match[1] : ""; // Use extracted language or empty string

    return (
      <div className="relative">
        <Button
          size="icon"
          variant={"outline"}
          className="absolute border-2 border-muted top-2 right-2 w-7 h-7 z-10"
          onClick={() => navigator.clipboard.writeText(codeContent)}
        >
          <Copy className="w-4 h-4" />
        </Button>
        {/* @ts-ignore */}
        <SyntaxHighlighter
          {...props}
          style={oneDark}
          language={language} // Use extracted language
          PreTag="div"
          wrapLongLines={true}
          codeTagProps={{
            style: { fontFamily: "JetBrains Mono, monospace" },
          }}
          className="rounded-lg"
        >
          {codeContent}
        </SyntaxHighlighter>
      </div>
    );
  }),
  a: ({ node, ...props }: any) => (
    <a
      className="font-medium text-primary underline underline-offset-4"
      {...props}
    />
  ),
};

export const MarkdownViewer = React.memo(({ children }: { children: string }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={markdownComponents}
    >
      {children}
    </ReactMarkdown>
  );
});
export const AssistantChatText = React.memo(
  ({
    entry,
    userMessageTimestamp,
  }: {
    entry: HistoryEntry;
    userMessageTimestamp?: string;
  }) => {
    const { parts, createdAt } = entry;
    // Calculate the full text content to be copied
    const fullText = parts
      .filter((part) => part.type === "text")
      // @ts-ignore
      .map((part) => part.content)
      .join("\n\n"); // Join multiple text parts with double newlines

    const handleCopy = async () => {
      if (fullText) {
        try {
          await navigator.clipboard.writeText(fullText);
          // console.log("Text copied to clipboard"); // Optional: add feedback later
        } catch (err) {
          console.error("Failed to copy text: ", err);
        }
      }
    };

    return (
      <div className="flex flex-col items-start group">
        {" "}
        {/* Add group class here */}
        <div className="space-y-2">
          {parts.map((part, index) => (
            <div key={index} className="w-full text-wrap">
              {part.type === "text" ? (
                <MarkdownViewer>{part.content}</MarkdownViewer>
              ) : (
                <NonTextPart part={part} />
              )}
            </div>
          ))}

          <div className="w-full flex flex-row  gap-2 items-center">
            {/* Add Copy Button */}
            {fullText && ( // Only show copy button if there is text content
              <Button
                size="icon"
                variant={"outline"}
                className="w-7 h-7"
                onClick={handleCopy}
              >
                {/* Import Copy icon from lucide-react */}
                <Copy />
              </Button>
            )}
            {/* Existing Refresh Button */}
            <Button size="icon" variant={"outline"} className="w-7 h-7">
              <RefreshCw />
            </Button>
            <div className="w-1 h-1  rounded-full bg-muted-foreground" />
            {userMessageTimestamp && (
              <span className="text-xs text-muted-foreground">
                {(() => {
                  const diffMs =
                    new Date(createdAt).getTime() -
                    new Date(userMessageTimestamp).getTime();
                  return diffMs < 1000
                    ? `${diffMs}ms`
                    : `${(diffMs / 1000).toFixed(1)}s`;
                })()}
              </span>
            )}
          </div>
        </div>
        {/* Add timestamp */}
        <div className="text-left text-xs text-gray-500 dark:text-gray-400 mt-1 pl-1">
          <span
            title={new Date(createdAt).toLocaleString()}
            className="opacity-0 group-hover:opacity-100"
          >
            {formatRelativeTime(createdAt)}
          </span>{" "}
          {/* Hide by default, show on hover */}
        </div>
      </div>
    );
  },
);

export const ChatTextarea = ({
  onSendMessage,
  isLoading,
}: {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}) => {
  const [text, setText] = useState("");

  const handleSubmit = () => {
    if (!text.trim() || isLoading) return;
    onSendMessage(text);
    setText("");
  };

  return (
    <div className="relative max-w-6xl w-full">
      <Textarea
        rows={3}
        placeholder="Message Control.."
        className="w-full rounded-xl pb-[20px] bg-background/90 resize-none"
        value={text}
        onChange={(e) => setText(e.target.value)}
        onKeyDown={(event) => {
          if (event.ctrlKey && event.key === "Enter") {
            event.preventDefault();
            handleSubmit();
          }
        }}
        disabled={isLoading}
      />
      <div className="absolute bottom-2 right-2 flex justify-between gap-2 w-full z-50">
        <Button
          size="icon"
          className="ml-auto gap-2 mr-1"
          onClick={handleSubmit}
          disabled={!text.trim() || isLoading}
        >
          <ArrowUp />
        </Button>
      </div>
    </div>
  );
};
type Part =
  | { type: "text"; content: string }
  | { type: "toolCall"; name: string; args: string[] };

// Define HistoryEntry type based on Go struct
type HistoryEntry = {
  role: "user" | "assistant";
  parts: Part[];
  createdAt: string; // Go time.Time will be string in JSON
};

type Convo = {
  working_dir: string;
  history: HistoryEntry[]; // Use the new HistoryEntry type
};

export const Chat = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [convo, setConvo] = useState<Convo | undefined>(undefined);
  const scrollAreaRef = useRef<HTMLDivElement>(null); // Ref for the scrollable area
  const lastMessageRef = useRef<HTMLDivElement>(null); // Ref for the last message element
  const accumulatedContentRef = useRef<string>(""); // Ref to accumulate streaming content
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [streamingContent, setStreamingContent] = useState<string>("");
  const [isStreaming, setIsStreaming] = useState(false);
  const hasHandledInitial = useRef(false);
  const streamingTimeoutRef = useRef<number | null>(null);
  const isAutoScrollingRef = useRef(true);
  const { startStreaming, addChunk, endStreaming } = useStreamingPerformance();

  // Optimized scroll to bottom function
  const scrollToBottom = useCallback((behavior: ScrollBehavior = "smooth") => {
    if (lastMessageRef.current && isAutoScrollingRef.current) {
      lastMessageRef.current.scrollIntoView({ behavior });
    }
  }, []);

  // Debounced streaming content update
  const updateStreamingContent = useCallback(() => {
    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
    }

    streamingTimeoutRef.current = window.setTimeout(() => {
      setStreamingContent(accumulatedContentRef.current);
      scrollToBottom("instant");
    }, 50); // Reduced from 100ms to 50ms for better responsiveness
  }, [scrollToBottom]);

  const getConvo = async () => {
    try {
      const convo = await GetConvo(id!);
      console.log(convo);
      setConvo(convo as any);
      setError(null);
    } catch (err) {
      console.error("Error loading conversation:", err);
      setError("Failed to load conversation");
    }
  };

  const handleSendMessage = async (message: string) => {
    if (!id) return;

    setIsLoading(true);
    setIsStreaming(true);
    setError(null);
    setStreamingContent("");

    try {
      // Start streaming
      await SendMessageStream(id, message);
    } catch (error) {
      console.error("Error sending message:", error);
      setError(
        error instanceof Error ? error.message : "Failed to send message",
      );
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  useEffect(() => {
    if (!id) return;

    // Set up event listeners for streaming
    const unsubscribeMessageAdded = EventsOn("message_added", (data: any) => {
      if (data.convoID === id && data.role === "user") {
        // Refresh conversation to show user message
        getConvo();
      }
    });

    const unsubscribeStreamStart = EventsOn("stream_start", (data: any) => {
      if (data.convoID === id) {
        setIsStreaming(true);
        // Reset accumulated ref and state at the start of a new stream
        accumulatedContentRef.current = "";
        setStreamingContent("");
        startStreaming();
      }
    });

    const unsubscribeStreamChunk = EventsOn("stream_chunk", (data: any) => {
      if (data.convoID === id) {
        // Append to ref and trigger debounced update
        accumulatedContentRef.current += data.content;
        addChunk();
        updateStreamingContent();
      }
    });

    const unsubscribeStreamComplete = EventsOn(
      "stream_complete",
      (data: any) => {
        if (data.convoID === id) {
          // Ensure final content is rendered
          setStreamingContent(accumulatedContentRef.current);
          setIsStreaming(false);
          setIsLoading(false);
          endStreaming();
          // Refresh conversation to show complete response
          getConvo();
          // Clear ref after complete
          accumulatedContentRef.current = "";
        }
      },
    );

    const unsubscribeStreamError = EventsOn("stream_error", (data: any) => {
      if (data.convoID === id) {
        setError(data.error);
        // Ensure current content is rendered before stopping
        setStreamingContent(accumulatedContentRef.current);
        setIsStreaming(false);
        setIsLoading(false);
        endStreaming();
        // Clear ref after error
        accumulatedContentRef.current = "";
      }
    });

    return () => {
      unsubscribeMessageAdded();
      unsubscribeStreamStart();
      unsubscribeStreamChunk();
      unsubscribeStreamComplete();
      unsubscribeStreamError();
    };
  }, [id]);

  // Cleanup streaming timeout on unmount or when streaming stops
  useEffect(() => {
    return () => {
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, []);

  // Update dependencies for event handlers
  useEffect(() => {
    if (!id) return;

    return () => {
      // Cleanup timeout when component unmounts or id changes
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, [id, updateStreamingContent]);

  useEffect(() => {
    getConvo();

    // Handle initial message streaming
    const initialMessage = searchParams.get("initial");
    if (initialMessage && !hasHandledInitial.current && id) {
      hasHandledInitial.current = true;
      // Clear the URL parameter
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        newParams.delete("initial");
        return newParams;
      });

      // Start streaming the initial message
      handleSendMessage(decodeURIComponent(initialMessage));
    }
  }, [id, searchParams]);

  // Optimized scroll behavior - only scroll when conversation changes
  useEffect(() => {
    scrollToBottom("smooth");
  }, [convo, scrollToBottom]);

  // Handle scroll detection to pause auto-scrolling when user scrolls up
  const handleScroll = useCallback(() => {
    if (!scrollAreaRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    // Update auto-scroll preference based on user scroll position
    isAutoScrollingRef.current = isNearBottom;
  }, []);

  return (
    <div className="relative flex flex-col max-h-screen w-full">
      <Menu />
      <ScrollArea
        ref={scrollAreaRef}
        className="flex flex-col w-full h-[calc(100vh-200px)] max-w-7xl mx-auto overflow-x-auto "
        onScroll={handleScroll}
      >
        <div className="flex-grow p-4 space-y-4 ">
          {error && (
            <div className="bg-destructive/10 border border-destructive/20 text-destructive p-3 rounded-xl">
              <p className="text-sm font-medium">Error</p>
              <p className="text-sm">{error}</p>
            </div>
          )}
          {optimizedMessages.map(({ entry, userMessageTimestamp, key }) => {
            return entry.role == "user" ? (
              <UserChatBubble
                key={key}
                entry={entry}
              />
            ) : (
              <AssistantChatText
                key={key}
                entry={entry}
                userMessageTimestamp={userMessageTimestamp}
              />
            );
          })}
          {isStreaming && streamingContent && (
            <div className="flex flex-col items-start group">
              <div className="space-y-2">
                <div className="w-full text-wrap">
                  <MarkdownViewer>{streamingContent}</MarkdownViewer>
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-current rounded-full animate-bounce"></div>
                    <div
                      className="w-1 h-1 bg-current rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-1 h-1 bg-current rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                  <span>Streaming...</span>
                </div>
              </div>
            </div>
          )}
          {isLoading && !isStreaming && (
            <div className="flex justify-start">
              <div className="bg-muted p-3 rounded-xl">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div
                    className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.1s" }}
                  ></div>
                  <div
                    className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.2s" }}
                  ></div>
                </div>
              </div>
            </div>
          )}
          <div ref={lastMessageRef}></div>
        </div>
      </ScrollArea>
      <div className="flex items-center justify-center w-full p-5">
        <ChatTextarea onSendMessage={handleSendMessage} isLoading={isLoading} />
      </div>
    </div>
  );
};
