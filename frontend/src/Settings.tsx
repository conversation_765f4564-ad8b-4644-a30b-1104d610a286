import { Input } from "@/components/ui/input";
import { ModeToggle } from "./components/mode-toggle";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { Menu } from "./components/menu";
import { <PERSON><PERSON> } from "./components/ui/button";
import { SetConfig, GetConfig } from "../wailsjs/go/main/App";
import { ChevronLeft } from "lucide-react";
import { Separator } from "./components/ui/separator";
import {
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  Select,
} from "./components/ui/select";
import { useTheme } from "./components/theme-provider";

// Updated interface to match Go backend Config struct
interface AppConfig {
  model: string;
  keys: { [provider: string]: string };
  openaiApiUrl: string; // Added field for OpenAI compatible API URL
}

export function Settings() {
  const deepseekIcon = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      style={{ flex: "none", lineHeight: "1" }}
      viewBox="0 0 24 24"
      width="18"
      height="18"
    >
      <path
        fill="#4D6BFE"
        d="M23.748 4.482c-.254-.124-.364.113-.512.234-.051.039-.094.09-.137.136-.372.397-.806.657-1.373.626-.829-.046-1.537.214-2.163.848-.133-.782-.575-1.248-1.247-1.548-.352-.156-.708-.311-.955-.65-.172-.241-.219-.51-.305-.774-.055-.16-.11-.323-.293-.35-.2-.031-.278.136-.356.276-.313.572-.434 1.202-.422 1.84.027 1.436.633 2.58 1.838 3.393.137.093.172.187.129.323-.082.28-.18.552-.266.833-.055.179-.137.217-.329.14a5.526 5.526 0 0 1-1.736-1.18c-.857-.828-1.631-1.742-2.597-2.458a11.365 11.365 0 0 0-.689-.471c-.985-.957.13-1.743.388-1.836.27-.098.093-.432-.779-.428-.872.004-1.67.295-2.687.684a3.055 3.055 0 0 1-.465.137 9.597 9.597 0 0 0-2.883-.102c-1.885.21-3.39 1.102-4.497 2.623C.082 8.606-.231 10.684.152 12.85c.403 2.284 1.569 4.175 3.36 5.653 1.858 1.533 3.997 2.284 6.438 2.14 1.482-.085 3.133-.284 4.994-1.86.47.234.962.327 1.78.397.63.059 1.236-.03 1.705-.128.735-.156.684-.837.419-.961-2.155-1.004-1.682-.595-2.113-.926 1.096-1.296 2.746-2.642 3.392-7.003.05-.347.007-.565 0-.845-.004-.17.035-.237.23-.256a4.173 4.173 0 0 0 1.545-.475c1.396-.763 1.96-2.015 2.093-3.517.02-.23-.004-.467-.247-.588zM11.581 18c-2.089-1.642-3.102-2.183-3.52-2.16-.392.024-.321.471-.235.763.09.288.207.486.371.739.114.167.192.416-.113.603-.673.416-1.842-.14-1.897-.167-1.361-.802-2.5-1.86-3.301-3.307-.774-1.393-1.224-2.887-1.298-4.482-.02-.386.093-.522.477-.592a4.696 4.696 0 0 1 1.529-.039c2.132.312 3.946 1.265 5.468 2.774.868.86 1.525 1.887 2.202 2.891.72 1.066 1.494 2.082 2.48 2.914.348.292.625.514.891.677-.802.09-2.14.11-3.054-.614zm1-6.44a.306.306 0 0 1 .415-.287.302.302 0 0 1 .2.288.306.306 0 0 1-.31.307.303.303 0 0 1-.304-.308zm3.11 1.596c-.2.081-.399.151-.59.16a1.245 1.245 0 0 1-.798-.254c-.274-.23-.47-.358-.552-.758a1.73 1.73 0 0 1 .016-.588c.07-.327-.008-.537-.239-.727-.187-.156-.426-.199-.688-.199a.559.559 0 0 1-.254-.078.253.253 0 0 1-.114-.358c.028-.054.16-.186.192-.21.356-.202.767-.136 1.146.016.352.144.618.408 1.001.782.391.451.462.576.685.914.176.265.336.537.445.848.067.195-.019.354-.25.452z"
      />
    </svg>
  );
  const geminiIcon = (
    <svg height="18" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <title>Gemini</title>
      <defs>
        <linearGradient
          id="lobe-icons-gemini-fill"
          x1="0%"
          x2="68.73%"
          y1="100%"
          y2="30.395%"
        >
          <stop offset="0%" stop-color="#1C7DFF"></stop>
          <stop offset="52.021%" stop-color="#1C69FF"></stop>
          <stop offset="100%" stop-color="#F0DCD6"></stop>
        </linearGradient>
      </defs>
      <path
        d="M12 24A14.304 14.304 0 000 12 14.304 14.304 0 0012 0a14.305 14.305 0 0012 12 14.305 14.305 0 00-12 12"
        fill="url(#lobe-icons-gemini-fill)"
        fill-rule="nonzero"
      ></path>
    </svg>
  );

  const { exposedTheme: theme } = useTheme();
  // Dummy icon for OpenAI compatible, replace with actual icon if available
  const openaiCompatibleIcon = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="256"
      height="260"
      preserveAspectRatio="xMidYMid"
      viewBox="0 0 256 260"
    >
      <path
        fill={theme == "light" ? "#000" : "#fff"}
        d="M239.184 106.203a64.716 64.716 0 0 0-5.576-53.103C219.452 28.459 191 15.784 163.213 21.74A65.586 65.586 0 0 0 52.096 45.22a64.716 64.716 0 0 0-43.23 31.36c-14.31 24.602-11.061 55.634 8.033 76.74a64.665 64.665 0 0 0 5.525 53.102c14.174 24.65 42.644 37.324 70.446 31.36a64.72 64.72 0 0 0 48.754 21.744c28.481.025 53.714-18.361 62.414-45.481a64.767 64.767 0 0 0 43.229-31.36c14.137-24.558 10.875-55.423-8.083-76.483Zm-97.56 136.338a48.397 48.397 0 0 1-31.105-11.255l1.535-.87 51.67-29.825a8.595 8.595 0 0 0 4.247-7.367v-72.85l21.845 12.636c.218.111.37.32.409.563v60.367c-.056 26.818-21.783 48.545-48.601 48.601Zm-104.466-44.61a48.345 48.345 0 0 1-5.781-32.589l1.534.921 51.722 29.826a8.339 8.339 0 0 0 8.441 0l63.181-36.425v25.221a.87.87 0 0 1-.358.665l-52.335 30.184c-23.257 13.398-52.97 5.431-66.404-17.803ZM23.549 85.38a48.499 48.499 0 0 1 25.58-21.333v61.39a8.288 8.288 0 0 0 4.195 7.316l62.874 36.272-21.845 12.636a.819.819 0 0 1-.767 0L41.353 151.53c-23.211-13.454-31.171-43.144-17.804-66.405v.256Zm179.466 41.695-63.08-36.63L161.73 77.86a.819.819 0 0 1 .768 0l52.233 30.184a48.6 48.6 0 0 1-7.316 87.635v-61.391a8.544 8.544 0 0 0-4.4-7.213Zm21.742-32.69-1.535-.922-51.619-30.081a8.39 8.39 0 0 0-8.492 0L99.98 99.808V74.587a.716.716 0 0 1 .307-.665l52.233-30.133a48.652 48.652 0 0 1 72.236 50.391v.205ZM88.061 139.097l-21.845-12.585a.87.87 0 0 1-.41-.614V65.685a48.652 48.652 0 0 1 79.757-37.346l-1.535.87-51.67 29.825a8.595 8.595 0 0 0-4.246 7.367l-.051 72.697Zm11.868-25.58 28.138-16.217 28.188 16.218v32.434l-28.086 16.218-28.188-16.218-.052-32.434Z"
      />
    </svg>
  );

  const models = {
    gemini: {
      icon: geminiIcon,
      models: [
        { name: "Gemini 1.5 Flash", id: "gemini:gemini-1.5-flash" },
        { name: "Gemini 1.5 Pro", id: "gemini:gemini-1.5-pro" },
        { name: "Gemini 2.0 Flash", id: "gemini:gemini-2.0-flash" },
        { name: "Gemini 2.0 Pro", id: "gemini:gemini-2.0-pro" },
        { name: "Gemini 2.5 Flash", id: "gemini:gemini-2.5-flash" },
        { name: "Gemini 2.5 Pro", id: "gemini:gemini-2.5-pro" },
      ],
    },
    openai: {
      icon: openaiCompatibleIcon,
      models: [
        {
          name: "GPT 4o",
          id: "openai:gpt-4o",
        },
        {
          name: "GPT 4o-mini",
          id: "openai:gpt-4o-mini",
        },
        {
          name: "GPT4.1",
          id: "openai:gpt-4.1",
        },
        {
          name: "GPT4.1-mini",
          id: "openai:gpt-4.1-mini",
        },
        {
          name: "o4 mini",
          id: "openai:o4-mini",
        },
        {
          name: "o3",
          id: "openai:o3",
        },
        {
          name: "o3 mini",
          id: "openai:o3-mini",
        },
      ],
    },
    deepseek: {
      icon: deepseekIcon,
      models: [
        { name: "Deepseek Chat", id: "deepseek:deepseek-chat" },
        { name: "Deepseek Reasoner", id: "deepseek:deepseek-reasoner" },
      ],
    },
    // New OpenAI compatible provider
    "openai-compatible": {
      icon: openaiCompatibleIcon,
      models: [
        // Add a placeholder model as the actual model will come from the API endpoint
        { name: "Custom Endpoint", id: "openai-compatible:custom" },
      ],
    },
  };

  const navigate = useNavigate();
  // State now holds the entire config object
  const [config, setConfig] = useState<AppConfig>({
    model: "",
    keys: {},
    openaiApiUrl: "",
  });

  useEffect(() => {
    async function loadConfig() {
      try {
        const loadedConfig: AppConfig = (await GetConfig()) as any;
        console.log("Loaded config:", loadedConfig);

        // Ensure keys map is initialized
        if (!loadedConfig.keys) {
          loadedConfig.keys = {};
        }

        // Set initial selected model if none is set in config
        if (!loadedConfig.model) {
          const firstProviderKey = Object.keys(models)[0];
          if (firstProviderKey) {
            // @ts-ignore - Ignoring for simplicity as in original code
            const firstModel = models[firstProviderKey].models[0];
            if (firstModel) {
              loadedConfig.model = firstModel.id;
            }
          }
        }
        // Ensure openaiApiUrl is initialized
        if (!loadedConfig.openaiApiUrl) {
          loadedConfig.openaiApiUrl = "";
        }

        setConfig(loadedConfig);
      } catch (error) {
        console.error("Failed to load config:", error);
        // If loading fails, initialize with default config structure
        setConfig({ model: "", keys: {}, openaiApiUrl: "" });
      }
    }
    loadConfig();
  }, []); // Empty dependency array ensures this runs once on mount

  const handleModelChange = async (modelId: string) => {
    const newConfig = { ...config, model: modelId };
    setConfig(newConfig);
    try {
      await SetConfig(newConfig);
    } catch (error) {
      console.error("Failed to save model config:", error);
    }
  };

  const handleKeyChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newKey = e.target.value;
    const newConfig = { ...config }; // Create a copy to avoid direct state mutation

    // Find the provider name from the selected model ID
    const selectedModelId = config.model;
    if (selectedModelId) {
      const providerName = selectedModelId.split(":")[0]; // Extract provider prefix

      // Ensure the keys map exists and update the specific provider\'s key
      if (!newConfig.keys) {
        newConfig.keys = {};
      }
      newConfig.keys[providerName] = newKey;

      setConfig(newConfig); // Update local state

      try {
        await SetConfig(newConfig); // Save to backend
      } catch (error) {
        console.error("Failed to save API key config:", error);
      }
    }
  };

  const handleApiUrlChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newApiUrl = e.target.value;
    const newConfig = { ...config, openaiApiUrl: newApiUrl }; // Update the API URL field
    setConfig(newConfig); // Update local state
    try {
      await SetConfig(newConfig); // Save to backend
    } catch (error) {
      console.error("Failed to save API URL config:", error);
    }
  };

  // Determine the current provider name based on the selected model
  const currentProviderName = config.model ? config.model.split(":")[0] : null;
  // Get the API key for the current provider, or an empty string if not found
  const currentApiKey =
    currentProviderName && config.keys
      ? config.keys[currentProviderName] || ""
      : "";

  return (
    <div className=" flex flex-col h-screen w-full">
      <Menu />
      <div className="flex flex-col gap-4 p-4 h-full overflow-auto">
        {/* Back button and title */}
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="rounded-full h-8 w-8"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ChevronLeft size={18} />
          </Button>
          <h1 className="text-2xl font-semibold">Settings</h1>
        </div>

        <Separator />

        {/* Appearance Section */}
        <h2 className="text-xl font-bold">Appearance</h2>
        <ModeToggle />
        <Separator />

        {/* Model Configuration Section */}
        <h2 className="text-xl font-bold">Model Configuration</h2>
        {/* Wrap label and select in flex-col gap-2 */}
        <div className="flex flex-col gap-2">
          <h1 className="text-sm font-semibold">Provider</h1>{" "}
          {/* Removed -mb-2 */}
          {/* @ts-ignore */}
          <Select value={config.model || ""} onValueChange={handleModelChange}>
            <SelectTrigger className="max-w-xl w-full">
              {config.model ? (
                <div className="flex items-center gap-2">
                  {
                    Object.values(models).find((p) =>
                      p.models.some((m) => m.id === config.model),
                    )?.icon
                  }
                  {
                    Object.values(models)
                      .flatMap((p) => p.models)
                      .find((m) => m.id === config.model)?.name
                  }
                </div>
              ) : (
                "Select a model"
              )}
            </SelectTrigger>
            <SelectContent>
              {Object.entries(models).map(([providerName, providerData]) => (
                <SelectGroup key={providerName}>
                  <SelectLabel className="capitalize text-xs">
                    {providerName}
                  </SelectLabel>
                  {providerData.models.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center gap-2">
                        {providerData.icon}
                        {model.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectGroup>
              ))}{" "}
            </SelectContent>{" "}
          </Select>
        </div>

        {/* Conditionally render API URL input for OpenAI compatible provider */}
        {currentProviderName === "openai-compatible" && (
          // Wrap label and input in flex-col gap-2
          <div className="flex flex-col gap-2">
            <h1 className="text-sm font-semibold">API URL</h1>{" "}
            {/* Removed -mb-2 */}
            <Input
              type="text" // Changed to text for URL
              placeholder="https://api.openai.com/v1" // Placeholder for API URL
              className="max-w-xl"
              value={config.openaiApiUrl} // Use the new state variable
              onChange={handleApiUrlChange} // Use the new handler
            />
          </div>
        )}

        {/* Wrap label and input in flex-col gap-2 */}
        <div className="flex flex-col gap-2">
          <h1 className="text-sm font-semibold">Key</h1> {/* Removed -mb-2 */}
          <Input
            type="password"
            placeholder="sk-..."
            className="max-w-xl"
            // Get the key from the config.keys map based on the current provider
            value={currentApiKey}
            onChange={handleKeyChange}
            // Disable input if no model is selected (and thus no provider is determined)
            disabled={!config.model}
          />
        </div>
        {/* The descriptive paragraph below the key input doesn't need wrapping */}
        <p className="text-sm text-muted-foreground mt-2">
          Your API key is stored locally and not shared.
        </p>
      </div>
    </div>
  );
}
