import { Sidebar, SidebarContent } from "@/components/ui/sidebar";
import { But<PERSON> } from "./ui/button";
import { Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function ChatsSidebar() {
  const navigate = useNavigate();
  return (
    <Sidebar variant="floating">
      <SidebarContent className="p-2">
        <Button
          variant={"secondary"}
          className="border"
          onClick={() => navigate("/")}
        >
          <Plus /> New task
        </Button>
      </SidebarContent>
    </Sidebar>
  );
}
