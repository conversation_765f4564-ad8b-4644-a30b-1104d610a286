import { Home, LogOut, Settings } from "lucide-react";
import { useEffect } from "react";
import { Quit } from "../../wailsjs/runtime/runtime";

import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "./ui/command";
import React from "react";
import { useNavigate } from "react-router-dom";
import { DisableEsc, EnableEsc } from "../../wailsjs/go/main/App";
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";

export function CommandMenuNoButton({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const navigate = useNavigate();

  useEffect(() => {
    if (open) {
      DisableEsc();
    } else {
      EnableEsc();
    }
    // Clean up the effect if the component unmounts while dialog is open
    return () => {
      if (open) {
        EnableEsc();
      }
    };
  }, [open]);

  const nav = (p: string) => {
    navigate(p);
    onOpenChange(false);
  };

  return (
    <CommandDialog open={open} onOpenChange={onOpenChange}>
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup heading="Navigate">
          <CommandItem onSelect={() => nav("/")}>
            <Home className="h-4 w-4" />
            Home
          </CommandItem>
          <CommandItem onSelect={() => nav("/settings")}>
            <Settings className="h-4 w-4" />
            Settings
          </CommandItem>
        </CommandGroup>
        {/* Example command: Quit Application */}
        <CommandGroup heading="Application">
          <CommandItem onSelect={() => Quit()}>
            <LogOut /> Quit
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
}
export function CommandMenu({
  className,
  onClick,
  ...props
}: React.ComponentPropsWithoutRef<"button">) {
  const [open, setOpen] = React.useState(false);

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };
    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    setOpen(true);
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        className={cn(
          "relative h-8 w-full  ml-auto mr-3 justify-start  bg-muted/50 text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-56 xl:w-64",
          className, // Merge passed className
        )}
        onClick={handleButtonClick} // Use the custom handler
        {...props} // Spread remaining props
      >
        <span className="hidden lg:inline-flex">Search or Navigate..</span>
        <span className="inline-flex lg:hidden">Search...</span>
        <kbd className="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>
      <CommandMenuNoButton open={open} onOpenChange={setOpen} />
    </>
  );
}
