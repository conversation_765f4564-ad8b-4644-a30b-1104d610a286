import { Setting<PERSON>, <PERSON> } from "lucide-react";
import { Quit } from "../../wailsjs/runtime/runtime";

import { But<PERSON> } from "./ui/button";
import { CommandMenu } from "./command-menu";
import { cn } from "@/lib/utils";
import { Link, useNavigate } from "react-router-dom";
import { SidebarTrigger } from "./ui/sidebar";

export function Menu() {
  const navigate = useNavigate();
  return (
    <div
      className={cn(
        "flex flex-row border-b-muted border-b p-4 justify-between w-full items-center",
      )}
    >
      <SidebarTrigger />
      <Link to="/" className="ml-">
        <h1 className="text-xl items-center select-none font-bold flex flex-row gap-2">
          <span>Control</span>
        </h1>
      </Link>
      <CommandMenu />
      <Button
        onClick={() => navigate("/settings")}
        variant="outline"
        size="icon"
        className="h-8 w-8 mr-3"
      >
        <Settings />
      </Button>
      <Button
        variant="ghost"
        className="w-8 h-8"
        size="icon"
        onClick={() => Quit()}
      >
        <X size={16} />
      </Button>
    </div>
  );
}
