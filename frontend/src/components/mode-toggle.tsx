import { useTheme } from "@/components/theme-provider";

import { cn } from "@/lib/utils"; // Assuming a utils file with a cn helper

export function ModeToggle() {
  const { setTheme, theme } = useTheme();

  return (
    <div className="flex flex-row gap-3 max-w-xl w-full ">
      {" "}
      {/* Added max-w-md and mx-auto */}
      <div className="flex flex-col gap-2 flex-1">
        {" "}
        {/* Added flex-1 */}
        <button
          onClick={() => setTheme("light")}
          className={cn(
            "h-[90px] w-full border-gray-400/20 border rounded-2xl pl-4 pt-5 bg-gray-200 transition-all duration-200", // Changed w-[100px] to w-full
            theme === "light" &&
              "ring-2 ring-ring ring-offset-2 ring-offset-background",
          )}
        >
          <div className="w-full h-full border-gray-400/40 bg-white border-l border-t rounded-br-2xl rounded-tl-lg relative">
            <h1 className="font-bold top-1 left-2 absolute dark:text-black">
              Aa
            </h1>
          </div>
        </button>
        <h4 className="font-semibold">Light</h4>{" "}
      </div>
      <div className="flex flex-col gap-2 flex-1">
        {" "}
        {/* Added flex-1 */}
        <button
          onClick={() => setTheme("dark")}
          className={cn(
            "h-[90px] w-full border-gray-600/20 border rounded-2xl pl-4 pt-5 bg-gray-900 transition-all duration-200", // Changed w-[100px] to w-full
            theme === "dark" &&
              "ring-2 ring-ring ring-offset-2 ring-offset-background",
          )}
        >
          <div className="w-full h-full border-gray-400/40 bg-gray-800 border-l border-t rounded-br-2xl rounded-tl-lg relative">
            <h1 className="font-bold top-1 left-2 absolute text-gray-100">
              Aa
            </h1>
          </div>
        </button>
        <h4 className="font-semibold">Dark</h4>
      </div>
      <div className="flex flex-col gap-2 flex-1">
        {" "}
        {/* Added flex-1 */}
        <button
          onClick={() => setTheme("system")}
          className={cn(
            "h-[90px] w-full border-gray-400/20 border rounded-2xl overflow-hidden relative p-0 transition-all duration-200", // Changed w-[100px] to w-full
            theme === "system" &&
              "ring-2 ring-ring ring-offset-2 ring-offset-background",
          )}
        >
          <div className="absolute inset-0 flex">
            {/* Left half - Light theme */}
            <div className="w-1/2 h-full bg-gray-200 pl-2 pt-5">
              <div className="w-full h-full border-gray-400/40 bg-white border-l border-t rounded-tl-lg relative">
                <h1 className="font-bold top-1 left-2 absolute dark:text-black">
                  Aa
                </h1>
              </div>
            </div>

            {/* Divider */}
            <div className="w-[2px] h-full bg-gray-400/30 z-10"></div>

            {/* Right half - Dark theme */}
            <div className="w-1/2 h-full bg-gray-900 pl-2 pt-5">
              <div className="w-full h-full border-gray-400/40 bg-gray-800  rounded-tl-lg border-l border-t relative">
                <h1 className="font-bold top-1 left-2 absolute text-gray-100">
                  Aa
                </h1>
              </div>
            </div>
          </div>
        </button>
        <h4 className="font-semibold">System</h4>
      </div>
    </div>
  );
}
